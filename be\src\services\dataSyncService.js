const recommendationService = require('./recommendationService');
const userService = require('./userService');
const expertService = require('./expertService');
const { pool } = require('../config/database');
// Simple logger utility
const logger = {
  info: (message, data) => {
    console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  error: (message, data) => {
    console.error(`[ERROR] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  debug: (message, data) => {
    console.log(`[DEBUG] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  }
};

class DataSyncService {
  constructor() {
    this.syncInProgress = false;
    this.lastSyncTimes = {
      users: null,
      experts: null,
      interactions: null
    };
  }

  /**
   * Perform full data synchronization
   */
  async performFullSync() {
    if (this.syncInProgress) {
      throw new Error('Sync already in progress');
    }

    this.syncInProgress = true;
    const results = {};

    try {
      logger.info('Starting full data synchronization with recommender service');

      // Sync users
      try {
        const users = await userService.getAllActiveUsers();
        if (users.length > 0) {
          const userSyncResult = await recommendationService.syncUsers(users);
          results.users = {
            total: users.length,
            synced: userSyncResult?.synced || 0,
            errors: userSyncResult?.errors || 0
          };
          this.lastSyncTimes.users = new Date();
          logger.info(`Synced ${results.users.synced} users`);
        }
      } catch (error) {
        logger.error('Failed to sync users:', error);
        results.users = { error: error.message };
      }

      // Sync experts
      try {
        const experts = await expertService.getAllActiveExperts();
        if (experts.length > 0) {
          const expertSyncResult = await recommendationService.syncExperts(experts);
          results.experts = {
            total: experts.length,
            synced: expertSyncResult?.synced || 0,
            errors: expertSyncResult?.errors || 0
          };
          this.lastSyncTimes.experts = new Date();
          logger.info(`Synced ${results.experts.synced} experts`);
        }
      } catch (error) {
        logger.error('Failed to sync experts:', error);
        results.experts = { error: error.message };
      }

      // Sync recent interactions
      try {
        const interactions = await this.getRecentInteractions();
        if (interactions.length > 0) {
          const interactionSyncResult = await recommendationService.syncInteractions(interactions);
          results.interactions = {
            total: interactions.length,
            synced: interactionSyncResult?.synced || 0,
            errors: interactionSyncResult?.errors || 0
          };
          this.lastSyncTimes.interactions = new Date();
          logger.info(`Synced ${results.interactions.synced} interactions`);
        }
      } catch (error) {
        logger.error('Failed to sync interactions:', error);
        results.interactions = { error: error.message };
      }

      logger.info('Full data synchronization completed', results);
      return results;

    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Perform incremental sync (only recent changes)
   */
  async performIncrementalSync() {
    if (this.syncInProgress) {
      throw new Error('Sync already in progress');
    }

    this.syncInProgress = true;
    const results = {};

    try {
      logger.info('Starting incremental data synchronization');

      // Sync recent users (last 7 days)
      try {
        const users = await userService.getRecentUsers(500);
        if (users.length > 0) {
          const userSyncResult = await recommendationService.syncUsers(users);
          results.users = {
            total: users.length,
            synced: userSyncResult?.synced || 0
          };
        }
      } catch (error) {
        logger.error('Failed to sync recent users:', error);
        results.users = { error: error.message };
      }

      // Sync recently updated experts
      try {
        const experts = await this.getRecentlyUpdatedExperts();
        if (experts.length > 0) {
          const expertSyncResult = await recommendationService.syncExperts(experts);
          results.experts = {
            total: experts.length,
            synced: expertSyncResult?.synced || 0
          };
        }
      } catch (error) {
        logger.error('Failed to sync recent experts:', error);
        results.experts = { error: error.message };
      }

      // Sync recent interactions
      try {
        const interactions = await this.getRecentInteractions(24); // Last 24 hours
        if (interactions.length > 0) {
          const interactionSyncResult = await recommendationService.syncInteractions(interactions);
          results.interactions = {
            total: interactions.length,
            synced: interactionSyncResult?.synced || 0
          };
        }
      } catch (error) {
        logger.error('Failed to sync recent interactions:', error);
        results.interactions = { error: error.message };
      }

      return results;

    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Get recently updated experts
   */
  async getRecentlyUpdatedExperts(hours = 24) {
    try {
      const query = `
        SELECT 
          e.id,
          e.user_id,
          e.name,
          e.description,
          e.labels,
          e.pricing_percentage as price_per_message,
          e.total_chats,
          e.average_rating,
          e.total_reviews,
          e.created_at,
          e.updated_at,
          e.is_public as is_active
        FROM experts e
        WHERE e.is_public = true 
          AND e.updated_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
        ORDER BY e.updated_at DESC
      `;

      const [experts] = await pool.execute(query, [hours]);

      return experts.map(expert => ({
        id: expert.id,
        user_id: expert.user_id,
        name: expert.name,
        description: expert.description,
        labels: expert.labels ? JSON.parse(expert.labels) : [],
        category: expertService.extractCategoryFromLabels(expert.labels),
        price_per_message: expert.price_per_message || 0,
        total_chats: expert.total_chats || 0,
        average_rating: expert.average_rating || 0,
        total_reviews: expert.total_reviews || 0,
        created_at: expert.created_at,
        updated_at: expert.updated_at,
        is_active: expert.is_active
      }));
    } catch (error) {
      logger.error('Error getting recently updated experts:', error);
      throw error;
    }
  }

  /**
   * Get recent interactions from various sources
   */
  async getRecentInteractions(hours = 168) { // Default 7 days
    try {
      const interactions = [];

      // Get chat interactions
      const chatQuery = `
        SELECT 
          cs.user_id,
          cs.expert_id,
          'chat' as interaction_type,
          COUNT(cm.id) as message_count,
          SUM(cm.cost) as total_cost,
          MAX(cm.created_at) as created_at
        FROM chat_sessions cs
        JOIN chat_messages cm ON cs.id = cm.session_id
        WHERE cm.created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
        GROUP BY cs.user_id, cs.expert_id
      `;

      const [chatInteractions] = await pool.execute(chatQuery, [hours]);
      
      chatInteractions.forEach(interaction => {
        interactions.push({
          user_id: interaction.user_id,
          expert_id: interaction.expert_id,
          interaction_type: 'chat',
          message_count: interaction.message_count,
          total_cost: interaction.total_cost,
          created_at: interaction.created_at
        });
      });

      // Get review interactions
      const reviewQuery = `
        SELECT 
          user_id,
          expert_id,
          'rate' as interaction_type,
          rating,
          created_at
        FROM reviews
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
      `;

      const [reviewInteractions] = await pool.execute(reviewQuery, [hours]);
      
      reviewInteractions.forEach(interaction => {
        interactions.push({
          user_id: interaction.user_id,
          expert_id: interaction.expert_id,
          interaction_type: 'rate',
          rating: interaction.rating,
          created_at: interaction.created_at
        });
      });

      // Get sharing interactions
      const shareQuery = `
        SELECT 
          shared_by_user_id as user_id,
          expert_id,
          'share' as interaction_type,
          monitor_enabled,
          created_at
        FROM expert_shares
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
      `;

      const [shareInteractions] = await pool.execute(shareQuery, [hours]);
      
      shareInteractions.forEach(interaction => {
        interactions.push({
          user_id: interaction.user_id,
          expert_id: interaction.expert_id,
          interaction_type: 'share',
          monitor_enabled: interaction.monitor_enabled,
          created_at: interaction.created_at
        });
      });

      return interactions;
    } catch (error) {
      logger.error('Error getting recent interactions:', error);
      throw error;
    }
  }

  /**
   * Sync single user
   */
  async syncUser(userId) {
    try {
      const query = `
        SELECT 
          user_id as id,
          email,
          name as username,
          created_at,
          updated_at,
          1 as is_active
        FROM user
        WHERE user_id = ?
      `;

      const [users] = await pool.execute(query, [userId]);
      
      if (users.length > 0) {
        await recommendationService.syncUsers([users[0]]);
        return { success: true, user: users[0] };
      }
      
      return { success: false, error: 'User not found' };
    } catch (error) {
      logger.error('Error syncing single user:', error);
      throw error;
    }
  }

  /**
   * Sync single expert
   */
  async syncExpert(expertId) {
    try {
      const expert = await expertService.getExpertById(expertId);
      
      if (expert) {
        const expertData = {
          id: expert.id,
          user_id: expert.user_id,
          name: expert.name,
          description: expert.description,
          labels: expert.labels ? JSON.parse(expert.labels) : [],
          category: expertService.extractCategoryFromLabels(expert.labels),
          price_per_message: expert.pricing_percentage || 0,
          total_chats: expert.total_chats || 0,
          average_rating: expert.average_rating || 0,
          total_reviews: expert.total_reviews || 0,
          created_at: new Date(),
          updated_at: new Date(),
          is_active: expert.is_public
        };

        await recommendationService.syncExperts([expertData]);
        return { success: true, expert: expertData };
      }
      
      return { success: false, error: 'Expert not found' };
    } catch (error) {
      logger.error('Error syncing single expert:', error);
      throw error;
    }
  }

  /**
   * Get sync status
   */
  getSyncStatus() {
    return {
      syncInProgress: this.syncInProgress,
      lastSyncTimes: this.lastSyncTimes
    };
  }

  /**
   * Schedule automatic sync
   */
  scheduleAutoSync() {
    // Perform incremental sync every hour
    setInterval(async () => {
      try {
        await this.performIncrementalSync();
      } catch (error) {
        logger.error('Scheduled incremental sync failed:', error);
      }
    }, 60 * 60 * 1000); // 1 hour

    // Perform full sync daily at 2 AM
    const now = new Date();
    const nextFullSync = new Date();
    nextFullSync.setHours(2, 0, 0, 0);
    
    if (nextFullSync <= now) {
      nextFullSync.setDate(nextFullSync.getDate() + 1);
    }

    const timeUntilFullSync = nextFullSync.getTime() - now.getTime();
    
    setTimeout(() => {
      this.performFullSync().catch(error => {
        logger.error('Scheduled full sync failed:', error);
      });

      // Schedule daily full sync
      setInterval(async () => {
        try {
          await this.performFullSync();
        } catch (error) {
          logger.error('Scheduled full sync failed:', error);
        }
      }, 24 * 60 * 60 * 1000); // 24 hours
    }, timeUntilFullSync);

    logger.info('Automatic data synchronization scheduled');
  }
}

module.exports = new DataSyncService();