1:"$Sreact.fragment"
2:I[283,["445","static/chunks/445-a270c29adcc484b7.js","874","static/chunks/874-437a265a67d6cfee.js","640","static/chunks/640-3861b2d46cf11a5e.js","570","static/chunks/570-cc86703759a65c3f.js","283","static/chunks/283-5f09abd3a35b9fda.js","558","static/chunks/app/layout-31369cc942e91dd2.js"],"AuthProvider"]
3:I[6070,["445","static/chunks/445-a270c29adcc484b7.js","874","static/chunks/874-437a265a67d6cfee.js","640","static/chunks/640-3861b2d46cf11a5e.js","570","static/chunks/570-cc86703759a65c3f.js","283","static/chunks/283-5f09abd3a35b9fda.js","558","static/chunks/app/layout-31369cc942e91dd2.js"],"default"]
4:I[5332,["445","static/chunks/445-a270c29adcc484b7.js","874","static/chunks/874-437a265a67d6cfee.js","640","static/chunks/640-3861b2d46cf11a5e.js","570","static/chunks/570-cc86703759a65c3f.js","283","static/chunks/283-5f09abd3a35b9fda.js","558","static/chunks/app/layout-31369cc942e91dd2.js"],"SocketProvider"]
5:I[5506,["445","static/chunks/445-a270c29adcc484b7.js","874","static/chunks/874-437a265a67d6cfee.js","640","static/chunks/640-3861b2d46cf11a5e.js","570","static/chunks/570-cc86703759a65c3f.js","283","static/chunks/283-5f09abd3a35b9fda.js","558","static/chunks/app/layout-31369cc942e91dd2.js"],"default"]
6:I[7555,[],""]
7:I[1295,[],""]
8:I[9243,["445","static/chunks/445-a270c29adcc484b7.js","874","static/chunks/874-437a265a67d6cfee.js","640","static/chunks/640-3861b2d46cf11a5e.js","570","static/chunks/570-cc86703759a65c3f.js","283","static/chunks/283-5f09abd3a35b9fda.js","558","static/chunks/app/layout-31369cc942e91dd2.js"],""]
9:I[894,[],"ClientPageRoot"]
a:I[9690,["445","static/chunks/445-a270c29adcc484b7.js","874","static/chunks/874-437a265a67d6cfee.js","319","static/chunks/319-1a592f7cf421693a.js","520","static/chunks/app/login/page-204d06cb3d695ff5.js"],"default"]
d:I[9665,[],"OutletBoundary"]
f:I[4911,[],"AsyncMetadataOutlet"]
11:I[9665,[],"ViewportBoundary"]
13:I[9665,[],"MetadataBoundary"]
14:"$Sreact.suspense"
16:I[8393,[],""]
:HL["/_next/static/css/da8bf3df0df487d6.css","style"]
0:{"P":null,"b":"W-SnV-_lHl4EUK1kZdWZr","p":"","c":["","login"],"i":false,"f":[[["",{"children":["login",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/da8bf3df0df487d6.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":[["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"children":[["$","$L5",null,{}],["$","$L6",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L7",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]]}]}]}],["$","$L8",null,{"id":"affiliate-tracker","strategy":"afterInteractive","children":"\n            // Initialize affiliate tracking\n            if (typeof window !== 'undefined') {\n              const initTracking = async () => {\n                const urlParams = new URLSearchParams(window.location.search);\n                const refCode = urlParams.get('ref');\n                \n                if (refCode) {\n                  try {\n                    const response = await fetch('/api/affiliate/track/' + refCode, {\n                      method: 'GET',\n                      credentials: 'include'\n                    });\n                    \n                    if (response.ok) {\n                      console.log('Affiliate tracking successful');\n                    }\n                  } catch (error) {\n                    console.error('Affiliate tracking failed:', error);\n                  }\n                }\n              };\n              \n              initTracking();\n            }\n          "}]]}]}]]}],{"children":["login",["$","$1","c",{"children":[null,["$","$L6",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L7",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L9",null,{"Component":"$a","searchParams":{},"params":{},"promises":["$@b","$@c"]}],null,["$","$Ld",null,{"children":["$Le",["$","$Lf",null,{"promise":"$@10"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$L11",null,{"children":"$L12"}],null],["$","$L13",null,{"children":["$","div",null,{"hidden":true,"children":["$","$14",null,{"fallback":null,"children":"$L15"}]}]}]]}],false]],"m":"$undefined","G":["$16",[]],"s":false,"S":true}
b:{}
c:"$0:f:0:1:2:children:2:children:1:props:children:0:props:params"
12:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
e:null
17:I[8175,[],"IconMark"]
10:{"metadata":[["$","title","0",{"children":"AI Expert Marketplace - AI Trainer Hub"}],["$","meta","1",{"name":"description","content":"Connect with specialized AI experts tailored to your needs. From business consulting to creative solutions."}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L17","3",{}]],"error":null,"digest":"$undefined"}
15:"$10:metadata"
