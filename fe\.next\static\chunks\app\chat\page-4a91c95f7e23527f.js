(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[457],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>i,AuthProvider:()=>o});var r=s(5155),a=s(2115),n=s(5731);let l=(0,a.createContext)(void 0),o=e=>{let{children:t}=e,[s,o]=(0,a.useState)(null),[i,c]=(0,a.useState)(null),[d,m]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{let e=localStorage.getItem("token"),t=localStorage.getItem("user");if(e&&t)try{c(e),o(JSON.parse(t));let s=await n.R2.getProfile();o(s.user)}catch(e){console.error("Token validation failed:",e),localStorage.removeItem("token"),localStorage.removeItem("user"),c(null),o(null)}m(!1)})()},[]);let u=async(e,t)=>{try{let s=(await n.R2.login({phone:e,password:t})).user,r=s.token,a={user_id:s.user_id,phone:s.phone,name:s.name,email:s.email};o(a),c(r),localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(a))}catch(e){throw Error(e.message||"Login failed")}},x=async e=>{try{return await n.R2.register(e)}catch(e){throw Error(e.message||"Registration failed")}},g=async(e,t)=>{try{let s=(await n.R2.verifyOTP({phone:e,code:t})).user,r=s.token,a={user_id:s.user_id,phone:s.phone,name:s.name,email:s.email};o(a),c(r),localStorage.setItem("token",r),localStorage.setItem("user",JSON.stringify(a))}catch(e){throw Error(e.message||"OTP verification failed")}},h=async()=>{try{i&&await n.R2.logout()}catch(e){console.error("Logout API call failed:",e)}finally{o(null),c(null),localStorage.removeItem("token"),localStorage.removeItem("user")}},p=async e=>{try{return await n.R2.resendOTP(e)}catch(e){throw Error(e.message||"Failed to resend OTP")}},f=async e=>{try{return await n.R2.forgotPassword(e)}catch(e){throw Error(e.message||"Failed to request password reset")}},y=async(e,t,s)=>{try{await n.R2.resetPassword(e,t,s)}catch(e){throw Error(e.message||"Password reset failed")}};return(0,r.jsx)(l.Provider,{value:{user:s,token:i,isLoading:d,isAuthenticated:!!s&&!!i,login:u,register:x,verifyOTP:g,logout:h,resendOTP:p,forgotPassword:f,resetPassword:y},children:t})},i=()=>{let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1154:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(5155);s(2115);var a=s(9434);function n(e){let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},3109:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3731:(e,t,s)=>{Promise.resolve().then(s.bind(s,9077))},5169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5332:(e,t,s)=>{"use strict";s.d(t,{F:()=>c,SocketProvider:()=>d});var r=s(5155),a=s(2115),n=s(4298),l=s(283),o=s(9509);let i=(0,a.createContext)({socket:null,isConnected:!1,connectionError:null,joinChat:()=>{},leaveChat:()=>{},sendChatMessage:()=>{},currentChatRoom:null,reconnect:()=>{}}),c=()=>{let e=(0,a.useContext)(i);if(!e)throw Error("useSocket must be used within a SocketProvider");return e},d=e=>{let{children:t}=e,{token:s,isAuthenticated:c,isLoading:d}=(0,l.A)(),[m,u]=(0,a.useState)(null),[x,g]=(0,a.useState)(!1),[h,p]=(0,a.useState)(null),[f,y]=(0,a.useState)(null),b=(0,a.useRef)(0);(0,a.useEffect)(()=>{if(console.log("\uD83D\uDD0C Socket initialization check:",{isLoading:d,isAuthenticated:c,hasToken:!!s,tokenLength:null==s?void 0:s.length,tokenStart:(null==s?void 0:s.substring(0,20))+"..."}),d)return void console.log("\uD83D\uDD0C Auth still loading, waiting...");if(m&&(console.log("\uD83E\uDDF9 Cleaning up existing socket connection"),m.disconnect(),u(null),g(!1),y(null)),!c||!s){console.log("\uD83D\uDD0C User not authenticated, skipping socket connection"),p("Please log in to use real-time chat");return}let e=o.env.NEXT_PUBLIC_API_URL||"http://localhost:3001";console.log("\uD83D\uDD0C Initializing Socket.IO connection to:",e);let t=(0,n.io)(e,{auth:{token:s},transports:["websocket","polling"],timeout:2e4,reconnection:!0,reconnectionAttempts:5,reconnectionDelay:1e3,reconnectionDelayMax:5e3});return t.on("connect",()=>{console.log("✅ Socket connected:",t.id),g(!0),p(null),b.current=0}),t.on("disconnect",e=>{console.log("❌ Socket disconnected:",e),g(!1),y(null),"io server disconnect"===e&&t.connect()}),t.on("connect_error",e=>{console.error("\uD83D\uDD0C Socket connection error:",e),console.error("\uD83D\uDD0C Error details:",{message:e.message,name:e.name,stack:e.stack}),p(e.message||"Connection failed"),g(!1),b.current+=1,b.current>=5&&p("Failed to connect after multiple attempts")}),t.on("chat_joined",e=>{console.log("\uD83C\uDFE0 Joined chat room:",e),y(e.room)}),t.on("chat_error",e=>{console.error("\uD83D\uDCAC Chat error:",e)}),t.on("typing_start",e=>{console.log("⌨️ User started typing:",e)}),t.on("typing_stop",e=>{console.log("⌨️ User stopped typing:",e)}),t.on("error",e=>{console.error("\uD83D\uDD10 Socket authentication error:",e),e.message&&e.message.includes("token")?(p("Authentication failed - please log in again"),localStorage.removeItem("token")):p("Connection failed"),g(!1)}),u(t),()=>{console.log("\uD83E\uDDF9 Cleaning up socket connection on unmount/change"),t.disconnect()}},[d,c,s,m]);let j=(0,a.useCallback)((e,t)=>{if(!m||!x)return void console.warn("⚠️ Cannot join chat: socket not connected");console.log("\uD83C\uDFE0 Joining chat:",{expertId:e,sessionId:t}),m.emit("join_chat",{expertId:e,sessionId:t})},[m,x]),v=(0,a.useCallback)(()=>{if(!m||!x)return void console.warn("⚠️ Cannot leave chat: socket not connected");console.log("\uD83D\uDEAA Leaving chat"),m.emit("leave_chat"),y(null)},[m,x]),N=(0,a.useCallback)((e,t,s)=>{if(!m||!x)return void console.warn("⚠️ Cannot send message: socket not connected");console.log("\uD83D\uDCE4 Sending chat message:",{message:e.substring(0,50)+"...",expertId:t,sessionId:s}),m.emit("start_chat_stream",{message:e,expertId:t,sessionId:s})},[m,x]),w=(0,a.useCallback)(()=>{console.log("\uD83D\uDD04 Manual reconnection requested"),m&&m.disconnect(),u(null),g(!1),p(null),y(null),b.current=0,s&&setTimeout(()=>{console.log("\uD83D\uDD04 Reconnection will be handled by useEffect")},100)},[m,s]);return(0,r.jsx)(i.Provider,{value:{socket:m,isConnected:x,connectionError:h,joinChat:j,leaveChat:v,sendChatMessage:N,currentChatRoom:f,reconnect:w},children:t})}},5339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},9077:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>U});var r=s(5155),a=s(2115),n=s(5695),l=s(6874),o=s.n(l),i=s(5169),c=s(5731),d=s(6766),m=s(9946);let u=(0,m.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var x=s(2523),g=s(285),h=s(6695),p=s(1154),f=s(1007),y=s(9434);let b=e=>{let{role:t,content:s,isStreaming:n=!1,timestamp:l,cost:o,tokens:i,expertName:c,expertImageUrl:m,expertIcon:u="\uD83E\uDD16"}=e,x=(0,a.useRef)(null);return(0,a.useEffect)(()=>{n&&x.current&&x.current.scrollIntoView({behavior:"smooth",block:"nearest"})},[s,n]),(0,r.jsxs)("div",{ref:x,className:"flex gap-3 ".concat("user"===t?"justify-end":"justify-start"," group"),children:["assistant"===t&&(0,r.jsx)("div",{className:"flex-shrink-0",children:m?(0,r.jsx)(d.default,{src:(0,y.L)(m),alt:c||"AI Assistant",width:32,height:32,className:"w-8 h-8 object-cover rounded-full border border-gray-200"}):(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm",style:{backgroundColor:"#1E3A8A"},children:u})}),(0,r.jsxs)("div",{className:"flex flex-col max-w-[70%]",children:[(0,r.jsxs)("div",{className:"rounded-2xl px-4 py-3 ".concat("user"===t?"text-white shadow-lg":"bg-gray-50 text-gray-900 border border-gray-100"," ").concat(n?"animate-pulse":""),style:"user"===t?{backgroundColor:"#1E3A8A"}:{},children:[(0,r.jsx)("div",{className:"text-sm leading-relaxed",dangerouslySetInnerHTML:{__html:s.replace(/\n/g,"<br/>")}}),n&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-2 pt-2 border-t border-gray-200",children:[(0,r.jsx)(p.A,{className:"w-3 h-3 animate-spin text-gray-500"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Streaming..."})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1 px-2 ".concat("user"===t?"justify-end":"justify-start"),children:[(0,r.jsx)("span",{className:"text-xs text-gray-400",children:new Date(l).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})}),"assistant"===t&&!n&&(o||i)&&(0,r.jsxs)(r.Fragment,{children:[i&&(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",i.toLocaleString()," tokens"]}),o&&(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:["• ",new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(o)]})]})]})]}),"user"===t&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)(f.A,{className:"w-4 h-4 text-gray-600"})})})]})},j=e=>{let{expertName:t,expertImageUrl:s,expertIcon:a="\uD83E\uDD16"}=e;return(0,r.jsxs)("div",{className:"flex gap-3 justify-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:s?(0,r.jsx)(d.default,{src:(0,y.L)(s),alt:t||"AI Assistant",width:32,height:32,className:"w-8 h-8 object-cover rounded-full border border-gray-200"}):(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm",style:{backgroundColor:"#1E3A8A"},children:a})}),(0,r.jsxs)("div",{className:"rounded-2xl px-4 py-3 bg-gray-50 border border-gray-100 flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[t||"AI"," is thinking..."]})]})]})};var v=s(9785),N=s(3109),w=s(5339);let k=e=>{let{currentCost:t,totalTokens:s,balanceUpdate:n,isStreaming:l,className:o=""}=e,[i,c]=(0,a.useState)(0),[d,m]=(0,a.useState)(0);(0,a.useEffect)(()=>{if(t>i){let e=(t-i)/10,s=setInterval(()=>{c(r=>{let a=r+e;return a>=t?(clearInterval(s),t):a})},50);return()=>clearInterval(s)}c(t)},[t,i]),(0,a.useEffect)(()=>{if(s>d){let e=(s-d)/10,t=setInterval(()=>{m(r=>{let a=r+e;return a>=s?(clearInterval(t),s):a})},50);return()=>clearInterval(t)}m(s)},[s,d]);let u=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e);return(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm ".concat(o),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:l?"Live Cost":"Session Cost"})]}),l&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs text-green-600",children:"Live"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Current Cost:"}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[l&&i>0&&(0,r.jsx)(N.A,{className:"w-3 h-3 text-orange-500"}),(0,r.jsx)("span",{className:"text-sm font-semibold ".concat(l?"text-orange-600":"text-gray-900"),children:u(i)})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Tokens Used:"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:Math.round(d).toLocaleString()})]}),n&&(0,r.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,r.jsxs)("div",{className:"space-y-1",children:[n.pointsUsed>0&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Points Used:"}),(0,r.jsxs)("span",{className:"text-xs font-medium text-red-600",children:["-",n.pointsUsed.toLocaleString()]})]}),n.creditsUsed>0&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Credits Used:"}),(0,r.jsxs)("span",{className:"text-xs font-medium text-red-600",children:["-",u(n.creditsUsed)]})]}),n.generatesCommission&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,r.jsx)(N.A,{className:"w-3 h-3 text-green-500"}),(0,r.jsx)("span",{className:"text-xs text-green-600",children:"Generates commission"})]})]})}),i>5e4&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 pt-2 border-t border-orange-100",children:[(0,r.jsx)(w.A,{className:"w-3 h-3 text-orange-500"}),(0,r.jsx)("span",{className:"text-xs text-orange-600",children:"High usage detected"})]})]}),l&&(0,r.jsx)("div",{className:"mt-3 pt-2 border-t border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-1 h-1 bg-blue-500 rounded-full animate-bounce"}),(0,r.jsx)("div",{className:"w-1 h-1 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,r.jsx)("div",{className:"w-1 h-1 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,r.jsx)("span",{className:"text-xs text-blue-600",children:"Processing..."})]})})]})};var S=s(646);let C=(0,m.A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),I=e=>{let{isConnected:t,connectionError:s,onReconnect:a,className:n=""}=e,l=(()=>{if(s){let e=s.includes("log in")||s.includes("Authentication");return{icon:w.A,text:e?"Authentication Required":"Connection Error",color:e?"text-yellow-600":"text-red-600",bgColor:e?"bg-yellow-50":"bg-red-50",borderColor:e?"border-yellow-200":"border-red-200",detail:s}}return t?{icon:S.A,text:"Connected",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200",detail:"Real-time chat enabled"}:{icon:C,text:"Connecting...",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200",detail:"Establishing connection"}})(),o=l.icon;return(0,r.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-2 rounded-lg border ".concat(l.bgColor," ").concat(l.borderColor," ").concat(n),children:[(0,r.jsx)(o,{className:"w-4 h-4 ".concat(l.color)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"text-sm font-medium ".concat(l.color),children:l.text}),(0,r.jsx)("div",{className:"text-xs text-gray-500 truncate",children:l.detail})]}),!t&&!s&&(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-1 h-1 bg-yellow-500 rounded-full animate-bounce"}),(0,r.jsx)("div",{className:"w-1 h-1 bg-yellow-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,r.jsx)("div",{className:"w-1 h-1 bg-yellow-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),s&&a&&(0,r.jsx)("button",{onClick:a,className:"text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors",children:"Retry"})]})};var A=s(5332);let E=e=>{let{expert:t,sessionId:s,initialMessages:n=[]}=e,[l,c]=(0,a.useState)(""),m=(0,a.useRef)(null),{isConnected:p,connectionError:f,reconnect:v}=(0,A.F)(),{messages:N,isStreaming:w,isTyping:S,error:C,currentCost:E,totalTokens:_,balanceUpdate:U,sendMessage:D,clearError:F,addMessage:L}=(e=>{let{socket:t,isConnected:s,joinChat:r,leaveChat:n,sendChatMessage:l}=(0,A.F)(),{expertId:o,sessionId:i,onMessageComplete:c,onCostUpdate:d,onBalanceUpdate:m,onError:u}=e,x=o&&""!==o.trim(),[g,h]=(0,a.useState)({messages:[],isStreaming:!1,isTyping:!1,currentStreamId:null,connectionStatus:"disconnected",error:null,currentCost:0,totalTokens:0,balanceUpdate:null}),p=(0,a.useRef)(null),f=(0,a.useRef)(0),y=()=>(f.current+=1,"msg_".concat(Date.now(),"_").concat(f.current));(0,a.useEffect)(()=>{h(e=>({...e,connectionStatus:s?"connected":"disconnected"}))},[s]),(0,a.useEffect)(()=>(s&&x&&(console.log("\uD83C\uDFE0 Auto-joining chat for expert:",o),r(o,i)),()=>{s&&n()}),[s,x,o,i,r,n]),(0,a.useEffect)(()=>{if(!t)return;let e=e=>{console.log("\uD83D\uDE80 Stream started:",e),h(t=>({...t,isStreaming:!0,currentStreamId:e.streamId,error:null})),p.current={id:y(),role:"assistant",content:"",timestamp:Date.now(),isStreaming:!0},h(e=>({...e,messages:[...e.messages,p.current]}))},s=e=>{console.log("\uD83D\uDCE6 Stream chunk:",e),p.current&&e.streamId===g.currentStreamId&&e.content&&(p.current.content+=e.content,h(e=>({...e,messages:e.messages.map(e=>{var t;return e&&e.id===(null==(t=p.current)?void 0:t.id)?{...p.current}:e}).filter(e=>null!==e)})))},r=e=>{console.log("\uD83D\uDCB0 Cost update:",e),h(t=>({...t,currentCost:e.estimatedCost,totalTokens:e.totalTokens})),d&&d(e.estimatedCost,e.totalTokens)},a=e=>{console.log("✅ Stream complete:",e),p.current&&(p.current.isStreaming=!1,p.current.cost=e.cost,p.current.tokens=e.totalTokens,h(t=>({...t,messages:t.messages.map(e=>{var t;return e&&e.id===(null==(t=p.current)?void 0:t.id)?{...p.current}:e}).filter(e=>null!==e),isStreaming:!1,currentStreamId:null,currentCost:e.cost,totalTokens:e.totalTokens,balanceUpdate:e.balanceUsage})),c&&c(p.current),m&&e.balanceUsage&&m(e.balanceUsage),p.current=null)},n=e=>{console.error("❌ Stream error:",e),h(t=>({...t,isStreaming:!1,currentStreamId:null,error:e.error})),u&&u(e.error),p.current&&(h(e=>({...e,messages:e.messages.filter(e=>{var t;return e&&e.id!==(null==(t=p.current)?void 0:t.id)})})),p.current=null)},l=e=>{console.log("⌨️ Typing started:",e),h(e=>({...e,isTyping:!0}))},o=e=>{console.log("⌨️ Typing stopped:",e),h(e=>({...e,isTyping:!1}))};return t.on("stream_started",e),t.on("stream_chunk",s),t.on("cost_update",r),t.on("stream_complete",a),t.on("stream_error",n),t.on("typing_start",l),t.on("typing_stop",o),()=>{t.off("stream_started",e),t.off("stream_chunk",s),t.off("cost_update",r),t.off("stream_complete",a),t.off("stream_error",n),t.off("typing_start",l),t.off("typing_stop",o)}},[t,g.currentStreamId,c,d,m,u]);let b=(0,a.useCallback)(e=>{if(!e.trim()||g.isStreaming)return;if(!x){console.error("❌ Cannot send message: expertId is required"),h(e=>({...e,error:"Expert not loaded. Please wait and try again."}));return}let t={id:y(),role:"user",content:e.trim(),timestamp:Date.now()};h(e=>({...e,messages:[...e.messages,t],error:null})),console.log("\uD83D\uDCE4 Sending message via socket:",{expertId:o,message:e.substring(0,50)+"...",sessionId:i}),l(e.trim(),o,i)},[x,o,i,l,g.isStreaming]),j=(0,a.useCallback)(()=>{h(e=>({...e,error:null}))},[]),v=(0,a.useCallback)(e=>{if(!e||!e.content)return void console.warn("⚠️ Attempted to add null or empty message:",e);let t={...e,id:y(),content:e.content||""};h(e=>({...e,messages:[...e.messages.filter(e=>null!==e),t]}))},[]),N=(0,a.useCallback)(()=>{h(e=>({...e,messages:[],currentCost:0,totalTokens:0,balanceUpdate:null}))},[]);return{...g,sendMessage:b,clearError:j,addMessage:v,clearMessages:N}})({expertId:(null==t?void 0:t.id.toString())||"",sessionId:s,onMessageComplete:e=>{console.log("✅ Message completed:",e)},onCostUpdate:(e,t)=>{console.log("\uD83D\uDCB0 Cost updated:",{cost:e,tokens:t})},onBalanceUpdate:e=>{console.log("\uD83D\uDCB3 Balance updated:",e)},onError:e=>{console.error("❌ Chat error:",e)}}),T=(0,a.useRef)(!1);(0,a.useEffect)(()=>{n.length>0&&!T.current&&(console.log("\uD83D\uDCE5 Loading initial messages:",n.length),n.forEach(e=>{L({role:e.role,content:e.content,timestamp:new Date(e.timestamp||e.created_at).getTime()})}),T.current=!0)},[n,L]),(0,a.useEffect)(()=>{m.current&&m.current.scrollTo({top:m.current.scrollHeight,behavior:"smooth"})},[N,S]);let P=e=>e?e.includes("business")||e.includes("marketing")?"\uD83D\uDCBC":e.includes("code")||e.includes("programming")?"\uD83D\uDCBB":e.includes("creative")||e.includes("design")?"\uD83C\uDFA8":e.includes("education")||e.includes("learning")?"\uD83D\uDCDA":e.includes("health")||e.includes("medical")?"\uD83C\uDFE5":e.includes("finance")||e.includes("money")?"\uD83D\uDCB0":"\uD83E\uDD16":"\uD83E\uDD16",R=async()=>{if(l.trim()&&!w){if(!t||!t.id){console.warn("⚠️ Cannot send message: expert not loaded"),alert("Please wait for the expert to load before sending messages.");return}c(""),p?(console.log("\uD83D\uDCE4 Sending message with expert:",{expertId:null==t?void 0:t.id,message:l.substring(0,50)+"..."}),D(l)):(console.log("\uD83D\uDD04 Using fallback API for message:",l),alert("Real-time chat is not available. Please log in or refresh the page to enable streaming features."))}};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(o(),{href:t?"/expert/".concat(t.id):"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,r.jsx)(i.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:t?"Back to Profile":"Back to Home"})]}),t&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Chatting with"}),(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:t.name})]}),t.imageUrl?(0,r.jsx)(d.default,{src:(0,y.L)(t.imageUrl),alt:t.name,width:40,height:40,className:"w-10 h-10 object-cover rounded-full border-2 border-gray-200"}):(0,r.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center text-white text-sm",style:{backgroundColor:"#1E3A8A"},children:P(t.labels)})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsx)(h.Zp,{className:"bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl",children:(0,r.jsxs)("div",{className:"p-6",children:[t&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[t.imageUrl?(0,r.jsx)(d.default,{src:(0,y.L)(t.imageUrl),alt:t.name,width:48,height:48,className:"w-12 h-12 object-cover rounded-full border-2 border-white shadow-sm"}):(0,r.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white text-lg shadow-sm",style:{backgroundColor:"#1E3A8A"},children:P(t.labels)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:t.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:t.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,r.jsx)("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full",children:t.model}),(0,r.jsx)("span",{className:"text-xs ".concat(p?"text-green-600":"text-red-600"),children:p?"● Online":"● Offline"})]})]})]})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)(I,{isConnected:p,connectionError:f,onReconnect:v}),f&&f.includes("log in")&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700",children:["\uD83D\uDCA1 ",(0,r.jsx)("strong",{children:"Tip:"})," You can still use the regular chat without real-time features.",(0,r.jsx)(o(),{href:"/login",className:"underline ml-1",children:"Log in"})," to enable streaming chat."]})})]}),C&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-red-600",children:C}),(0,r.jsx)("button",{onClick:F,className:"text-red-600 hover:text-red-800 text-sm underline",children:"Dismiss"})]})}),(0,r.jsxs)("div",{ref:m,className:"h-[60vh] overflow-y-auto space-y-4 mb-6 px-2",style:{scrollbarWidth:"thin"},children:[0===N.length&&t&&(0,r.jsxs)("div",{className:"text-center mt-20",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCAC"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"Start a Conversation"}),(0,r.jsxs)("p",{className:"text-gray-500",children:["Hello! I'm ",t.name,". ",t.description," How can I assist you today?"]})]}),N.filter(e=>e&&e.id).map(e=>(0,r.jsx)(b,{role:e.role,content:e.content||"",isStreaming:e.isStreaming,timestamp:e.timestamp,cost:e.cost,tokens:e.tokens,expertName:null==t?void 0:t.name,expertImageUrl:null==t?void 0:t.imageUrl,expertIcon:t?P(t.labels):"\uD83E\uDD16"},e.id)),S&&(0,r.jsx)(j,{expertName:null==t?void 0:t.name,expertImageUrl:null==t?void 0:t.imageUrl,expertIcon:t?P(t.labels):"\uD83E\uDD16"})]}),(0,r.jsxs)("form",{className:"flex gap-3 items-end",onSubmit:e=>{e.preventDefault(),R()},children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(x.p,{value:l,onChange:e=>c(e.target.value),placeholder:t?"Ask ".concat(t.name," anything..."):"Type your message...",disabled:w||!p,className:"w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200",autoFocus:!0,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),R())}})}),(0,r.jsx)(g.$,{type:"submit",disabled:w||!l.trim()||!p,className:"px-6 py-3 rounded-xl text-white font-medium transition-all duration-200 hover:shadow-lg",style:{backgroundColor:"#1E3A8A"},children:(0,r.jsx)(u,{className:"w-5 h-5"})})]})]})})}),(0,r.jsxs)("div",{className:"lg:col-span-1 space-y-4",children:[(0,r.jsx)(k,{currentCost:E,totalTokens:_,balanceUpdate:U,isStreaming:w}),t&&(0,r.jsxs)(h.Zp,{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"Session Info"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Expert:"}),(0,r.jsx)("span",{className:"font-medium",children:t.name})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Model:"}),(0,r.jsx)("span",{className:"font-medium",children:t.model})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Messages:"}),(0,r.jsx)("span",{className:"font-medium",children:N.length})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Status:"}),(0,r.jsx)("span",{className:"font-medium ".concat(p?"text-green-600":"text-red-600"),children:p?"Connected":"Disconnected"})]})]})]})]})]})]})})};function _(){let e=(0,n.useSearchParams)(),t=e.get("expertId"),s=e.get("threadId"),[l,d]=(0,a.useState)(null),[m,u]=(0,a.useState)([]),[x,g]=(0,a.useState)(null),[h,p]=(0,a.useState)(!!t),[f,y]=(0,a.useState)(!1),[b,j]=(0,a.useState)(null);(0,a.useEffect)(()=>{t?(console.log("\uD83D\uDE80 Initializing chat with expertId:",t),v()):s?(console.log("\uD83D\uDE80 Loading chat with threadId:",s),N()):(console.log("⚠️ No expertId or threadId provided"),j("No expert or thread specified"))},[t,s]);let v=async()=>{try{console.log("\uD83D\uDCDE Loading expert data for ID:",t),p(!0),y(!0),j(null),console.log("\uD83D\uDCDE About to call api.getExpert with:",t);let e=await c.FH.getExpert(t);if(console.log("\uD83D\uDCDE Expert API response:",e),!e.success){console.error("❌ Failed to load expert:",e),j("Failed to load expert: ".concat(e.error||"Unknown error"));return}console.log("\uD83D\uDCDE Setting expert data:",e.expert),d(e.expert),console.log("✅ Expert loaded successfully:",e.expert.name),console.log("\uD83D\uDCDE Checking for active session for expert:",t);let s=await c.FH.getActiveSessionForExpert(t);if(console.log("\uD83D\uDCDE Session API response:",s),s.success&&s.session){let e=s.session;g(e.id.toString()),console.log("\uD83D\uDCDE Loading chat history for session:",e.id),await k(e.id),console.log("✅ Loaded existing session:",e.id,"with thread:",e.thread_id)}else console.log("\uD83C\uDD95 No existing session found, starting fresh chat"),u([{role:"assistant",content:"Hello! I'm ".concat(e.expert.name,". ").concat(e.expert.description," How can I assist you today?"),timestamp:Date.now()}])}catch(e){console.error("\uD83D\uDCA5 Failed to initialize chat with expert:",e),console.error("\uD83D\uDCA5 Error details:",{message:e instanceof Error?e.message:"Unknown error",stack:e instanceof Error?e.stack:void 0,expertId:t}),j("Failed to initialize chat: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{p(!1),y(!1)}},N=async()=>{s&&(console.log("\uD83D\uDCDE Loading chat history for thread:",s),await w(s))},w=async e=>{try{y(!0),j(null),console.log("\uD83D\uDCDE Loading messages for thread:",e),console.log("\uD83D\uDCDE Trying to get session info for thread:",e);try{let t=await c.FH.getUserChatSessions(50);if(t.success&&t.sessions){let s=t.sessions.find(t=>t.thread_id===e);if(s&&s.expert_id){console.log("\uD83D\uDCDE Found session with expert_id:",s.expert_id);let e=await c.FH.getExpert(s.expert_id.toString());e.success&&(d(e.expert),g(s.id.toString()),console.log("✅ Expert loaded from session:",e.expert.name))}}}catch(e){console.warn("⚠️ Could not load session info:",e)}let t=await c.FH.getThreadMessages(e);if(console.log("\uD83D\uDCDE Thread messages API response:",t),t.success&&t.messages){let e=t.messages.map(e=>({role:e.role,content:e.content,timestamp:e.timestamp||e.created_at}));if(u(e),console.log("✅ Loaded",e.length,"messages from thread"),!l&&t.expertId){console.log("\uD83D\uDCDE Loading expert from thread data, expertId:",t.expertId);try{let e=await c.FH.getExpert(t.expertId.toString());e.success&&(d(e.expert),console.log("✅ Expert loaded from thread:",e.expert.name))}catch(e){console.warn("⚠️ Could not load expert from thread:",e)}}}else console.error("❌ Failed to load thread messages:",t),j("Failed to load chat history: ".concat(t.error||"Unknown error"))}catch(e){console.error("\uD83D\uDCA5 Failed to load chat history:",e),j("Failed to load chat history: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{y(!1)}},k=async e=>{try{y(!0),j(null),console.log("\uD83D\uDCDE Loading chat history for session:",e);let t=await c.FH.getSessionMessages(e,50);if(console.log("\uD83D\uDCDE Session messages API response:",t),t.success&&t.messages){let s=t.messages.map(e=>({role:e.role,content:e.content,timestamp:e.timestamp||e.created_at}));u(s),console.log("✅ Loaded",s.length,"messages from session",e)}else console.error("❌ Failed to load session messages:",t),j("Failed to load session messages: ".concat(t.error||"Unknown error"))}catch(e){console.error("\uD83D\uDCA5 Failed to load chat history by session:",e),j("Failed to load session messages: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{y(!1)}};return b?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto p-6",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"⚠️"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"Error Loading Chat"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:b}),(0,r.jsxs)(o(),{href:"/",className:"inline-flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,r.jsx)(i.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Back to Home"})]})]})}):h||f?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4",style:{borderColor:"#1E3A8A"}}),(0,r.jsx)("p",{className:"text-gray-600",children:h?"Loading expert (ID: ".concat(t,")..."):"Loading chat history..."}),(0,r.jsxs)("p",{className:"text-sm text-gray-400 mt-2",children:["Debug: expertId=",t,", threadId=",s]})]})}):(console.log("\uD83D\uDD0D ChatComponent render:",{expert:l?{id:l.id,name:l.name}:null,expertId:t,isLoadingExpert:h,error:b}),(0,r.jsx)(E,{expert:l,sessionId:x||void 0,initialMessages:m}))}function U(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(_,{})})}}},e=>{e.O(0,[445,874,352,212,640,573,441,964,358],()=>e(e.s=3731)),_N_E=e.O()}]);