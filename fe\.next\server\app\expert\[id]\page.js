(()=>{var a={};a.id=55,a.ids=[55],a.modules={133:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(43210),f=c.n(e),g=c(64398),h=c(4780);let i=({rating:a,maxRating:b=5,size:c="md",interactive:e=!1,onRatingChange:i,className:j})=>{let[k,l]=f().useState(0),m={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"},n=()=>{e&&l(0)};return(0,d.jsxs)("div",{className:(0,h.cn)("flex items-center gap-1",j),children:[Array.from({length:b},(b,f)=>{let j=f+1;return(0,d.jsx)(g.A,{className:(0,h.cn)(m[c],(b=>{let c=k||a;return b<=c?"fill-yellow-400 text-yellow-400":b-.5<=c?"fill-yellow-400/50 text-yellow-400":"fill-gray-200 text-gray-200"})(j),e&&"cursor-pointer hover:scale-110 transition-transform"),onClick:()=>{e&&i&&i(j)},onMouseEnter:()=>{e&&l(j)},onMouseLeave:n},f)}),a>0&&(0,d.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:a.toFixed(1)})]})}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},22915:(a,b,c)=>{Promise.resolve().then(c.bind(c,44381))},23562:(a,b,c)=>{"use strict";c.d(b,{k:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,value:b=0,max:c=100,...e},g)=>{let h=Math.min(100,Math.max(0,b/c*100));return(0,d.jsx)("div",{ref:g,className:(0,f.cn)("relative h-4 w-full overflow-hidden rounded-full bg-gray-200",a),...e,children:(0,d.jsx)("div",{className:"h-full bg-blue-600 transition-all duration-300 ease-in-out",style:{width:`${h}%`}})})});g.displayName="Progress"},23595:(a,b,c)=>{Promise.resolve().then(c.bind(c,52215))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44381:(a,b,c)=>{"use strict";c.d(b,{default:()=>$});var d=c(60687),e=c(43210),f=c(30474),g=c(85814),h=c.n(g),i=c(16189),j=c(81620),k=c(62185),l=c(4780),m=c(133),n=c(29523),o=c(63503),p=c(91821),q=c(44493),r=c(54300);let s=({expertId:a,expertName:b,existingReview:c,onSuccess:f,onCancel:g})=>{let[h,i]=(0,e.useState)(c?.rating||0),[j,l]=(0,e.useState)(c?.review_text||""),[o,s]=(0,e.useState)(!1),[t,u]=(0,e.useState)(null),v=async b=>{if(b.preventDefault(),0===h)return void u("Please select a rating");try{let b;s(!0),u(null),(b=c?await k.FH.updateReview(c.id,{rating:h,reviewText:j.trim()||void 0}):await k.FH.createReview({expertId:a,rating:h,reviewText:j.trim()||void 0})).success?f?.(b.review):u(b.error||"Failed to submit review")}catch(a){u(a.message||"Failed to submit review")}finally{s(!1)}};return(0,d.jsxs)(q.Zp,{className:"w-full max-w-md",children:[(0,d.jsxs)(q.aR,{children:[(0,d.jsx)(q.ZB,{children:c?"Update Review":"Write a Review"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["Share your experience with ",b]})]}),(0,d.jsx)(q.Wu,{children:(0,d.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[t&&(0,d.jsx)(p.Fc,{variant:"destructive",children:t}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(r.J,{htmlFor:"rating",children:"Rating *"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{rating:h,interactive:!0,onRatingChange:i,size:"lg"}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["(",h>0?`${h} star${1!==h?"s":""}`:"Select rating",")"]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(r.J,{htmlFor:"reviewText",children:"Review (Optional)"}),(0,d.jsx)("textarea",{id:"reviewText",value:j,onChange:a=>l(a.target.value),placeholder:"Share your thoughts about this expert...",className:"w-full min-h-[100px] p-3 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",maxLength:1e3}),(0,d.jsxs)("div",{className:"text-xs text-gray-500 text-right",children:[j.length,"/1000 characters"]})]}),(0,d.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,d.jsx)(n.$,{type:"submit",disabled:o||0===h,className:"flex-1",children:o?"Submitting...":c?"Update Review":"Submit Review"}),g&&(0,d.jsx)(n.$,{type:"button",variant:"outline",onClick:g,disabled:o,children:"Cancel"})]})]})})]})},t=({isOpen:a,onClose:b,expertId:c,expertName:f,onReviewSubmitted:g})=>{let[h,i]=(0,e.useState)(null),[j,l]=(0,e.useState)(null),[m,q]=(0,e.useState)(null),[r,t]=(0,e.useState)(!0),u=(0,e.useCallback)(async()=>{try{t(!0);let a=await k.FH.canUserReview(c);if(a.success){if(l(a),i(a.canReview),"already_reviewed"===a.reason){let a=await k.FH.getUserReviews(1,100);if(a.success){let b=a.reviews.find(a=>a.expert_id===c);b&&(q(b),i(!0))}}}else i(!1),l({canReview:!1,message:a.error||"Unable to check review eligibility"})}catch(a){i(!1),l({canReview:!1,message:a.message||"Failed to check review eligibility"})}finally{t(!1)}},[c]);(0,e.useEffect)(()=>{a&&c&&u()},[a,c,u]);let v=()=>{i(null),l(null),q(null),t(!0),b()};return(0,d.jsx)(o.lG,{open:a,onOpenChange:v,children:(0,d.jsxs)(o.Cf,{className:"max-w-md",children:[(0,d.jsx)(o.c7,{children:(0,d.jsx)(o.L3,{children:m?"Update Your Review":"Write a Review"})}),r?(0,d.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):h?(0,d.jsx)(s,{expertId:c,expertName:f,existingReview:m?{id:m.id,rating:m.rating,review_text:m.review_text}:void 0,onSuccess:a=>{g?.(a),b()},onCancel:v}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(p.Fc,{variant:"destructive",children:j?.message||"You cannot review this expert at this time."}),j?.reason==="insufficient_interaction"&&(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,d.jsx)("p",{children:"To maintain review quality, you need to have at least 3 conversations with this expert before you can write a review."}),(0,d.jsxs)("p",{className:"mt-2",children:["Start chatting with ",f," to unlock the ability to review!"]})]}),(0,d.jsx)("div",{className:"flex justify-end",children:(0,d.jsx)(n.$,{onClick:v,variant:"outline",children:"Close"})})]})]})})};var u=c(23562);let v=({expertId:a,className:b})=>{let[c,f]=(0,e.useState)(null),[g,h]=(0,e.useState)(!0),[i,j]=(0,e.useState)(null);if((0,e.useEffect)(()=>{(async()=>{try{h(!0),j(null);let b=await k.FH.getExpertRatingStats(a);b.success?f(b.stats):j(b.error||"Failed to load rating statistics")}catch(a){j(a.message||"Failed to load rating statistics")}finally{h(!1)}})()},[a]),g)return(0,d.jsxs)(q.Zp,{className:b,children:[(0,d.jsx)(q.aR,{children:(0,d.jsx)(q.ZB,{className:"text-lg",children:"Rating Summary"})}),(0,d.jsx)(q.Wu,{className:"animate-pulse",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)("div",{className:"w-16 h-8 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded"})]}),Array.from({length:5}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"w-8 h-4 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"flex-1 h-2 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"w-8 h-4 bg-gray-200 rounded"})]},b))]})})]});if(i||!c)return(0,d.jsx)(q.Zp,{className:b,children:(0,d.jsx)(q.Wu,{className:"p-6 text-center text-gray-500",children:i||"No rating data available"})});if(0===c.total_reviews)return(0,d.jsxs)(q.Zp,{className:b,children:[(0,d.jsx)(q.aR,{children:(0,d.jsx)(q.ZB,{className:"text-lg",children:"Rating Summary"})}),(0,d.jsx)(q.Wu,{className:"text-center text-gray-500",children:(0,d.jsx)("p",{children:"No reviews yet"})})]});let l=parseFloat(c.average_rating);return(0,d.jsxs)(q.Zp,{className:b,children:[(0,d.jsx)(q.aR,{children:(0,d.jsx)(q.ZB,{className:"text-lg",children:"Rating Summary"})}),(0,d.jsx)(q.Wu,{className:"space-y-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-gray-900",children:l.toFixed(1)}),(0,d.jsx)(m.A,{rating:l,size:"sm"}),(0,d.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:[c.total_reviews," review",1!==c.total_reviews?"s":""]})]}),(0,d.jsx)("div",{className:"flex-1 space-y-2",children:[5,4,3,2,1].map(a=>{let b=c.rating_distribution[a],e=c.total_reviews>0?b/c.total_reviews*100:0;return(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1 w-12",children:[(0,d.jsx)("span",{children:a}),(0,d.jsx)(m.A,{rating:1,maxRating:1,size:"sm"})]}),(0,d.jsx)(u.k,{value:e,className:"flex-1 h-2"}),(0,d.jsx)("span",{className:"w-8 text-right text-gray-600",children:b})]},a)})})]})})]})},w=Symbol.for("constructDateFrom");function x(a,b){return"function"==typeof a?a(b):a&&"object"==typeof a&&w in a?a[w](b):a instanceof Date?new a.constructor(b):new Date(b)}let y={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function z(a){return (b={})=>{let c=b.width?String(b.width):a.defaultWidth;return a.formats[c]||a.formats[a.defaultWidth]}}let A={date:z({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:z({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:z({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},B={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function C(a){return(b,c)=>{let d;if("formatting"===(c?.context?String(c.context):"standalone")&&a.formattingValues){let b=a.defaultFormattingWidth||a.defaultWidth,e=c?.width?String(c.width):b;d=a.formattingValues[e]||a.formattingValues[b]}else{let b=a.defaultWidth,e=c?.width?String(c.width):a.defaultWidth;d=a.values[e]||a.values[b]}return d[a.argumentCallback?a.argumentCallback(b):b]}}function D(a){return(b,c={})=>{let d,e=c.width,f=e&&a.matchPatterns[e]||a.matchPatterns[a.defaultMatchWidth],g=b.match(f);if(!g)return null;let h=g[0],i=e&&a.parsePatterns[e]||a.parsePatterns[a.defaultParseWidth],j=Array.isArray(i)?function(a,b){for(let c=0;c<a.length;c++)if(b(a[c]))return c}(i,a=>a.test(h)):function(a,b){for(let c in a)if(Object.prototype.hasOwnProperty.call(a,c)&&b(a[c]))return c}(i,a=>a.test(h));return d=a.valueCallback?a.valueCallback(j):j,{value:d=c.valueCallback?c.valueCallback(d):d,rest:b.slice(h.length)}}}let E={code:"en-US",formatDistance:(a,b,c)=>{let d,e=y[a];if(d="string"==typeof e?e:1===b?e.one:e.other.replace("{{count}}",b.toString()),c?.addSuffix)if(c.comparison&&c.comparison>0)return"in "+d;else return d+" ago";return d},formatLong:A,formatRelative:(a,b,c,d)=>B[a],localize:{ordinalNumber:(a,b)=>{let c=Number(a),d=c%100;if(d>20||d<10)switch(d%10){case 1:return c+"st";case 2:return c+"nd";case 3:return c+"rd"}return c+"th"},era:C({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:C({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:a=>a-1}),month:C({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:C({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:C({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(a){return(b,c={})=>{let d=b.match(a.matchPattern);if(!d)return null;let e=d[0],f=b.match(a.parsePattern);if(!f)return null;let g=a.valueCallback?a.valueCallback(f[0]):f[0];return{value:g=c.valueCallback?c.valueCallback(g):g,rest:b.slice(e.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:a=>parseInt(a,10)}),era:D({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:D({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:a=>a+1}),month:D({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:D({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:D({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},F={};function G(a,b){return x(b||a,a)}function H(a){let b=G(a),c=new Date(Date.UTC(b.getFullYear(),b.getMonth(),b.getDate(),b.getHours(),b.getMinutes(),b.getSeconds(),b.getMilliseconds()));return c.setUTCFullYear(b.getFullYear()),a-c}function I(a,...b){let c=x.bind(null,a||b.find(a=>"object"==typeof a));return b.map(c)}function J(a,b){let c=G(a)-G(b);return c<0?-1:c>0?1:c}function K(a,b){return function(a,b,c){let d,e=c?.locale??F.locale??E,f=J(a,b);if(isNaN(f))throw RangeError("Invalid time value");let g=Object.assign({},c,{addSuffix:c?.addSuffix,comparison:f}),[h,i]=I(c?.in,...f>0?[b,a]:[a,b]),j=function(a,b,c){var d;return(d=void 0,a=>{let b=(d?Math[d]:Math.trunc)(a);return 0===b?0:b})((G(a)-G(b))/1e3)}(i,h),k=Math.round((j-(H(i)-H(h))/1e3)/60);if(k<2)if(c?.includeSeconds)if(j<5)return e.formatDistance("lessThanXSeconds",5,g);else if(j<10)return e.formatDistance("lessThanXSeconds",10,g);else if(j<20)return e.formatDistance("lessThanXSeconds",20,g);else if(j<40)return e.formatDistance("halfAMinute",0,g);else if(j<60)return e.formatDistance("lessThanXMinutes",1,g);else return e.formatDistance("xMinutes",1,g);else if(0===k)return e.formatDistance("lessThanXMinutes",1,g);else return e.formatDistance("xMinutes",k,g);if(k<45)return e.formatDistance("xMinutes",k,g);if(k<90)return e.formatDistance("aboutXHours",1,g);if(k<1440){let a=Math.round(k/60);return e.formatDistance("aboutXHours",a,g)}if(k<2520)return e.formatDistance("xDays",1,g);else if(k<43200){let a=Math.round(k/1440);return e.formatDistance("xDays",a,g)}else if(k<86400)return d=Math.round(k/43200),e.formatDistance("aboutXMonths",d,g);if((d=function(a,b,c){let[d,e,f]=I(void 0,a,a,b),g=J(e,f),h=Math.abs(function(a,b,c){let[d,e]=I(void 0,a,b);return 12*(d.getFullYear()-e.getFullYear())+(d.getMonth()-e.getMonth())}(e,f));if(h<1)return 0;1===e.getMonth()&&e.getDate()>27&&e.setDate(30),e.setMonth(e.getMonth()-g*h);let i=J(e,f)===-g;(function(a,b){let c=G(a,void 0);return+function(a,b){let c=G(a,b?.in);return c.setHours(23,59,59,999),c}(c,void 0)==+function(a,b){let c=G(a,b?.in),d=c.getMonth();return c.setFullYear(c.getFullYear(),d+1,0),c.setHours(23,59,59,999),c}(c,b)})(d)&&1===h&&1===J(d,f)&&(i=!1);let j=g*(h-i);return 0===j?0:j}(i,h))<12){let a=Math.round(k/43200);return e.formatDistance("xMonths",a,g)}{let a=d%12,b=Math.trunc(d/12);return a<3?e.formatDistance("aboutXYears",b,g):a<9?e.formatDistance("overXYears",b,g):e.formatDistance("almostXYears",b+1,g)}}(a,x(a,Date.now()),b)}var L=c(62688);let M=(0,L.A)("flag",[["path",{d:"M4 22V4a1 1 0 0 1 .4-.8A6 6 0 0 1 8 2c3 0 5 2 7.333 2q2 0 3.067-.8A1 1 0 0 1 20 4v10a1 1 0 0 1-.4.8A6 6 0 0 1 16 16c-3 0-5-2-8-2a6 6 0 0 0-4 1.528",key:"1jaruq"}]]),N=(0,L.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),O=(0,L.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),P=({expertId:a,showPagination:b=!0,limit:c=10,onReviewReport:f})=>{let[g,h]=(0,e.useState)([]),[i,j]=(0,e.useState)(!0),[l,o]=(0,e.useState)(null),[r,s]=(0,e.useState)(1),[t,u]=(0,e.useState)(1),[v,w]=(0,e.useState)(0),x=(0,e.useCallback)(async(b=1)=>{try{j(!0),o(null);let d=await k.FH.getExpertReviews(a,b,c);d.success?(h(d.reviews),s(d.pagination.page),u(d.pagination.totalPages),w(d.pagination.total)):o(d.error||"Failed to load reviews")}catch(a){o(a.message||"Failed to load reviews")}finally{j(!1)}},[a,c]);(0,e.useEffect)(()=>{x(1)},[a,x]);let y=a=>{a>=1&&a<=t&&x(a)};return i?(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((a,b)=>(0,d.jsx)(q.Zp,{className:"animate-pulse",children:(0,d.jsx)(q.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-full"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3"}),(0,d.jsx)("div",{className:"h-16 bg-gray-200 rounded"})]})]})})},b))}):l?(0,d.jsx)(p.Fc,{variant:"destructive",children:l}):0===g.length?(0,d.jsx)(q.Zp,{children:(0,d.jsx)(q.Wu,{className:"p-8 text-center",children:(0,d.jsx)("p",{className:"text-gray-500",children:"No reviews yet. Be the first to review this expert!"})})}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("h3",{className:"text-lg font-semibold",children:["Reviews (",v,")"]})}),(0,d.jsx)("div",{className:"space-y-4",children:g.map(a=>(0,d.jsx)(q.Zp,{children:(0,d.jsx)(q.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold",children:a.reviewer_name.charAt(0).toUpperCase()}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"font-medium text-gray-900",children:[a.reviewer_name,a.is_verified&&(0,d.jsx)("span",{className:"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Verified"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,d.jsx)(m.A,{rating:a.rating,size:"sm"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:K(new Date(a.created_at),{addSuffix:!0})})]})]}),f&&(0,d.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{var b;return b=a.id,void f?.(b)},className:"text-gray-400 hover:text-red-500",children:(0,d.jsx)(M,{className:"w-4 h-4"})})]}),a.review_text&&(0,d.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:a.review_text}),a.updated_at!==a.created_at&&(0,d.jsxs)("p",{className:"text-xs text-gray-400",children:["Updated ",K(new Date(a.updated_at),{addSuffix:!0})]})]})]})})},a.id))}),b&&t>1&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",(r-1)*c+1," to ",Math.min(r*c,v)," of ",v," reviews"]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>y(r-1),disabled:1===r,children:[(0,d.jsx)(N,{className:"w-4 h-4"}),"Previous"]}),(0,d.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,t)},(a,b)=>{let c;return c=t<=5||r<=3?b+1:r>=t-2?t-4+b:r-2+b,(0,d.jsx)(n.$,{variant:r===c?"default":"outline",size:"sm",onClick:()=>y(c),className:"w-8 h-8 p-0",children:c},c)})}),(0,d.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>y(r+1),disabled:r===t,children:["Next",(0,d.jsx)(O,{className:"w-4 h-4"})]})]})]})]})};var Q=c(3315),R=c(54184),S=c(96834),T=c(85726),U=c(96882);let V=(0,L.A)("shuffle",[["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 18h1.973a4 4 0 0 0 3.3-1.7l5.454-8.6a4 4 0 0 1 3.3-1.7H22",key:"1ailkh"}],["path",{d:"M2 6h1.972a4 4 0 0 1 3.6 2.2",key:"km57vx"}],["path",{d:"M22 18h-6.041a4 4 0 0 1-3.3-1.8l-.359-.45",key:"os18l9"}]]);var W=c(78122),X=c(64398),Y=c(33872);function Z({expertId:a,limit:b=6,title:c="Similar Experts",description:e="Other experts you might like",showRefreshButton:f=!0,className:g=""}){let h=(0,i.useRouter)(),{similarExperts:j,loading:k,error:l,referenceExpert:m,refresh:o,trackClick:r}=(0,R.uP)({expertId:a,limit:b,enabled:!!a}),s=async(a,b)=>{await r(a,b),h.push(`/experts/${a}`)},t=async(a,b,c)=>{c.stopPropagation(),await r(a,b),h.push(`/chat?expertId=${a}`)};return k?(0,d.jsxs)("div",{className:`space-y-4 ${g}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(T.E,{className:"h-6 w-32 mb-2"}),(0,d.jsx)(T.E,{className:"h-4 w-48"})]}),f&&(0,d.jsx)(T.E,{className:"h-9 w-24"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array.from({length:b}).map((a,b)=>(0,d.jsxs)(q.Zp,{className:"h-40",children:[(0,d.jsxs)(q.aR,{children:[(0,d.jsx)(T.E,{className:"h-5 w-3/4"}),(0,d.jsx)(T.E,{className:"h-4 w-1/2"})]}),(0,d.jsx)(q.Wu,{children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(T.E,{className:"h-4 w-2/3"}),(0,d.jsx)(T.E,{className:"h-8 w-full"})]})})]},b))})]}):l?(0,d.jsx)("div",{className:g,children:(0,d.jsxs)(p.Fc,{children:[(0,d.jsx)(U.A,{className:"h-4 w-4"}),(0,d.jsxs)(p.TN,{children:["Failed to load similar experts. ",f&&(0,d.jsx)(n.$,{variant:"link",className:"p-0 h-auto",onClick:o,children:"Try again"})]})]})}):0===j.length?(0,d.jsx)("div",{className:g,children:(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(V,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No similar experts found"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"We couldn't find experts similar to this one yet."}),f&&(0,d.jsxs)(n.$,{onClick:o,variant:"outline",children:[(0,d.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})}):(0,d.jsxs)("div",{className:`space-y-4 ${g}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:c}),(0,d.jsxs)("p",{className:"text-muted-foreground text-sm",children:[e,m&&(0,d.jsxs)("span",{children:[" based on ",(0,d.jsx)("strong",{children:m.name})]})]})]}),f&&(0,d.jsxs)(n.$,{onClick:o,variant:"outline",size:"sm",disabled:k,children:[(0,d.jsx)(W.A,{className:`h-4 w-4 mr-2 ${k?"animate-spin":""}`}),"Refresh"]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:j.map((a,b)=>(0,d.jsxs)(q.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>s(a.expert_id,b),children:[(0,d.jsxs)(q.aR,{className:"pb-3",children:[(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)(q.ZB,{className:"text-base line-clamp-1",children:a.name}),(0,d.jsx)(q.BT,{className:"text-xs mt-1",children:a.category})]})}),(0,d.jsx)("div",{className:"flex items-center gap-2 mt-2",children:(0,d.jsxs)(S.E,{variant:"outline",className:"text-xs",children:[Math.round(100*a.similarity_score),"% similar"]})})]}),(0,d.jsxs)(q.Wu,{className:"pt-0",children:[(0,d.jsx)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground mb-3",children:(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(X.A,{className:"h-3 w-3 fill-current text-yellow-400"}),(0,d.jsx)("span",{children:a.average_rating.toFixed(1)})]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(n.$,{size:"sm",className:"flex-1",onClick:c=>t(a.expert_id,b,c),children:[(0,d.jsx)(Y.A,{className:"h-3 w-3 mr-1"}),"Chat"]}),(0,d.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>s(a.expert_id,b),children:"View"})]})]})]},a.expert_id))})]})}let $=({expertId:a})=>{let[b,c]=(0,e.useState)(null),[g,o]=(0,e.useState)(!0),[p,q]=(0,e.useState)(null),[r,s]=(0,e.useState)(!1),[u,w]=(0,e.useState)(!1),[x,y]=(0,e.useState)("about"),z=(0,i.useRouter)(),A=async()=>{try{o(!0),q(null);let b=await k.FH.getExpert(a);b.success?c(b.expert):q(b.error||"Failed to load expert")}catch(a){q(a.message||"Failed to load expert")}finally{o(!1)}};return((0,e.useEffect)(()=>{A()},[a]),g)?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)(h(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,d.jsx)("span",{children:"←"}),(0,d.jsx)("span",{children:"Back to Marketplace"})]})}),(0,d.jsx)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 animate-pulse",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-8",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-32 h-32 bg-gray-200 rounded-full"})}),(0,d.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]})]})]})})]})}):p||!b?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)(h(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,d.jsx)("span",{children:"←"}),(0,d.jsx)("span",{children:"Back to Marketplace"})]})}),(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-8 max-w-md mx-auto",children:[(0,d.jsx)("div",{className:"text-red-600 text-6xl mb-4",children:"⚠️"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-red-800 mb-2",children:"Expert Not Found"}),(0,d.jsx)("p",{className:"text-red-600 mb-4",children:p||"This expert may not exist or is not publicly available."}),(0,d.jsx)(h(),{href:"/",className:"inline-block px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Return to Marketplace"})]})})]})}):(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)(h(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,d.jsx)("span",{children:"←"}),(0,d.jsx)("span",{children:"Back to Marketplace"})]})}),(0,d.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-900 to-indigo-900 p-8 text-white",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-6",children:[(0,d.jsxs)("div",{className:"relative",children:[b.imageUrl?(0,d.jsx)(f.default,{src:(0,l.L)(b.imageUrl),alt:b.name,width:128,height:128,className:"w-32 h-32 object-cover rounded-full border-4 border-white shadow-xl"}):(0,d.jsx)("div",{className:"w-32 h-32 rounded-full bg-white/20 backdrop-blur-sm border-4 border-white shadow-xl flex items-center justify-center text-6xl",children:(a=>a.includes("business")||a.includes("marketing")?"\uD83D\uDCBC":a.includes("code")||a.includes("programming")?"\uD83D\uDCBB":a.includes("creative")||a.includes("design")?"\uD83C\uDFA8":a.includes("education")||a.includes("learning")?"\uD83D\uDCDA":a.includes("health")||a.includes("medical")?"\uD83C\uDFE5":a.includes("finance")||a.includes("money")?"\uD83D\uDCB0":"\uD83E\uDD16")(b.labels)}),(0,d.jsx)("div",{className:"absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-3 h-3 bg-white rounded-full"})})]}),(0,d.jsxs)("div",{className:"flex-1 text-center md:text-left",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold mb-2",children:b.name}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 justify-center md:justify-start mb-4",children:[(0,d.jsx)("span",{className:"px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium",children:b.model}),(0,d.jsx)("span",{className:"px-3 py-1 bg-green-500 rounded-full text-sm font-medium",children:"● Online"})]}),b.averageRating&&b.averageRating>0?(0,d.jsxs)("div",{className:"flex items-center justify-center md:justify-start gap-2",children:[(0,d.jsx)(m.A,{rating:b.averageRating,size:"md"}),(0,d.jsxs)("span",{className:"text-white/90 text-sm",children:["(",b.totalReviews," review",1!==b.totalReviews?"s":"",")"]})]}):(0,d.jsx)("div",{className:"text-white/70 text-sm text-center md:text-left",children:"No reviews yet"})]})]})}),(0,d.jsxs)("div",{className:"p-8",children:[(0,d.jsxs)("div",{className:"flex gap-4 mb-8 border-b border-gray-200",children:[(0,d.jsx)("button",{onClick:()=>y("about"),className:`pb-4 px-2 font-medium transition-colors ${"about"===x?"text-blue-600 border-b-2 border-blue-600":"text-gray-600 hover:text-gray-900"}`,children:"About"}),(0,d.jsxs)("button",{onClick:()=>y("reviews"),className:`pb-4 px-2 font-medium transition-colors ${"reviews"===x?"text-blue-600 border-b-2 border-blue-600":"text-gray-600 hover:text-gray-900"}`,children:["Reviews (",b.totalReviews||0,")"]})]}),"about"===x&&(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"About This Expert"}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-xl p-6",children:(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:b.description})})]}),b.labels&&b.labels.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Areas of Expertise"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-3",children:b.labels.map((a,b)=>(0,d.jsxs)("span",{className:"px-4 py-2 bg-blue-100 text-blue-800 rounded-full font-medium hover:bg-blue-200 transition-colors",children:["#",a]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Expert Details"}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6 space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"AI Model:"}),(0,d.jsx)("span",{className:"font-semibold",children:b.model})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Response Time:"}),(0,d.jsx)("span",{className:"font-semibold text-green-600",children:"Instant"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Availability:"}),(0,d.jsx)("span",{className:"font-semibold text-green-600",children:"24/7"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Added:"}),(0,d.jsx)("span",{className:"font-semibold",children:new Date(b.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100",children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Start Conversation"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Ready to get expert assistance? Start chatting now for instant responses."}),(0,d.jsx)("button",{onClick:()=>{z.push(`/chat?expertId=${a}`)},className:"w-full py-4 px-6 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1 text-lg mb-4",style:{backgroundColor:"#1E3A8A"},children:"\uD83D\uDE80 Start Chat"}),(0,d.jsxs)(n.$,{onClick:()=>w(!0),variant:"outline",className:"w-full mb-3",children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Share Expert"]}),(0,d.jsx)(n.$,{onClick:()=>s(!0),variant:"outline",className:"w-full",children:"Write a Review"})]}),(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-6 border border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"What You Get"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-green-600 text-sm",children:"✓"})}),(0,d.jsx)("span",{className:"text-gray-700",children:"Instant responses"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-green-600 text-sm",children:"✓"})}),(0,d.jsx)("span",{className:"text-gray-700",children:"Specialized knowledge"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-green-600 text-sm",children:"✓"})}),(0,d.jsx)("span",{className:"text-gray-700",children:"24/7 availability"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-green-600 text-sm",children:"✓"})}),(0,d.jsx)("span",{className:"text-gray-700",children:"Secure conversations"})]})]})]})]})]}),"reviews"===x&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsx)(v,{expertId:b.id}),(0,d.jsx)("div",{className:"flex items-center justify-center",children:(0,d.jsx)(n.$,{onClick:()=>s(!0),size:"lg",className:"px-8",children:"Write a Review"})})]}),(0,d.jsx)(P,{expertId:b.id,showPagination:!0,limit:10})]})]})]}),(0,d.jsx)("div",{className:"mt-12",children:(0,d.jsx)(Z,{expertId:b.id,limit:6,title:"Similar Experts",description:"Other experts you might like",showRefreshButton:!0,className:"bg-gray-50 rounded-xl p-6"})}),r&&(0,d.jsx)(t,{isOpen:r,onClose:()=>s(!1),expertId:b.id,expertName:b.name,onReviewSubmitted:()=>{A()}}),(0,d.jsx)(Q.A,{expert:b,isOpen:u,onOpenChange:w,showTrigger:!1,onShareCreated:a=>{console.log("Share created:",a),w(!1)},onCancel:()=>w(!1)})]})})}},52215:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\components\\\\ExpertProfile.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertProfile.tsx","default")},54184:(a,b,c)=>{"use strict";c.d(b,{GI:()=>h,uP:()=>i});var d=c(43210),e=c(62185);class f{async getPersonalizedRecommendations(a={}){try{let b=new URLSearchParams;return a.limit&&b.append("limit",a.limit.toString()),a.algorithm&&b.append("algorithm",a.algorithm),a.category&&b.append("category",a.category),a.exclude&&a.exclude.length>0&&b.append("exclude",a.exclude.join(",")),(await e.FH.get(`${this.baseUrl}/personalized?${b.toString()}`)).data.data}catch(a){return console.error("Failed to get personalized recommendations:",a),{recommendations:[],algorithm_used:"fallback",total_available:0,cache_hit:!1,generated_at:new Date().toISOString(),fallback:!0,error:a.response?.data?.message||"Service unavailable"}}}async getSimilarExperts(a,b=10){try{return(await e.FH.get(`${this.baseUrl}/similar/${a}?limit=${b}`)).data.data}catch(b){return console.error("Failed to get similar experts:",b),{similar_experts:[],reference_expert:{id:a,name:"Unknown Expert"}}}}async refreshRecommendations(a="hybrid"){try{return await e.FH.post(`${this.baseUrl}/refresh`,{body:{algorithm:a}}),!0}catch(a){return console.error("Failed to refresh recommendations:",a),!1}}async trackInteraction(a){try{return await e.FH.post(`${this.baseUrl}/track`,{body:a}),!0}catch(a){return console.error("Failed to track interaction:",a),!1}}async getServiceHealth(){try{return(await e.FH.get(`${this.baseUrl}/health`)).data.data}catch(a){return console.error("Failed to get service health:",a),{status:"unhealthy",error:a.response?.data?.message||"Service unavailable"}}}async trackRecommendationClick(a,b,c,d){await this.trackInteraction({expert_id:a,interaction_type:"click",recommendation_position:b,algorithm_used:c,metadata:d})}async trackRecommendationDismiss(a,b,c,d){await this.trackInteraction({expert_id:a,interaction_type:"dismiss",recommendation_position:b,algorithm_used:c,metadata:{dismiss_reason:d}})}async trackRecommendationFavorite(a,b,c){await this.trackInteraction({expert_id:a,interaction_type:"favorite",recommendation_position:b,algorithm_used:c})}async trackRecommendationChatStart(a,b,c){await this.trackInteraction({expert_id:a,interaction_type:"chat_start",recommendation_position:b,algorithm_used:c})}constructor(){this.baseUrl="/api/recommendations"}}let g=new f;function h(a={}){let[b,c]=(0,d.useState)([]),[e,f]=(0,d.useState)(!0),[i,j]=(0,d.useState)(null),[k,l]=(0,d.useState)("hybrid"),[m,n]=(0,d.useState)(0),[o,p]=(0,d.useState)(!1),[q,r]=(0,d.useState)(null),s=(0,d.useCallback)(async()=>{try{f(!0),j(null);let b=await g.getPersonalizedRecommendations(a);c(b.recommendations),l(b.algorithm_used),n(b.total_available),p(b.fallback||!1),r(b.generated_at),b.error&&j(b.error)}catch(a){j(a.message||"Failed to load recommendations"),c([])}finally{f(!1)}},[a]),t=(0,d.useCallback)(async()=>{await s()},[s]),u=(0,d.useCallback)(async(a,b)=>{await g.trackRecommendationClick(a,b,k)},[k]),v=(0,d.useCallback)(async(a,b,c)=>{await g.trackRecommendationDismiss(a,b,k,c)},[k]),w=(0,d.useCallback)(async(a,b)=>{await g.trackRecommendationFavorite(a,b,k)},[k]),x=(0,d.useCallback)(async(a,b)=>{await g.trackRecommendationChatStart(a,b,k)},[k]);return{recommendations:b,loading:e,error:i,algorithmUsed:k,totalAvailable:m,isFallback:o,lastUpdated:q,refresh:t,trackClick:u,trackDismiss:v,trackFavorite:w,trackChatStart:x}}function i(a){let[b,c]=(0,d.useState)([]),[e,f]=(0,d.useState)(!1),[h,i]=(0,d.useState)(null),[j,k]=(0,d.useState)(null),l=(0,d.useCallback)(async()=>{if(a.expertId&&!1!==a.enabled)try{f(!0),i(null);let b=await g.getSimilarExperts(a.expertId,a.limit||10);c(b.similar_experts),k(b.reference_expert)}catch(a){i(a.message||"Failed to load similar experts"),c([])}finally{f(!1)}},[a.expertId,a.limit,a.enabled]);return{similarExperts:b,loading:e,error:h,referenceExpert:j,refresh:(0,d.useCallback)(async()=>{await l()},[l]),trackClick:(0,d.useCallback)(async(b,c)=>{await g.trackInteraction({expert_id:b,interaction_type:"click",recommendation_position:c,algorithm_used:"similar",metadata:{reference_expert_id:a.expertId}})},[a.expertId])}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},74075:a=>{"use strict";a.exports=require("zlib")},78122:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85726:(a,b,c)=>{"use strict";c.d(b,{E:()=>f});var d=c(60687),e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},91821:(a,b,c)=>{"use strict";c.d(b,{Fc:()=>i,TN:()=>j});var d=c(60687),e=c(43210),f=c(24224),g=c(4780);let h=(0,f.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=e.forwardRef(({className:a,variant:b,...c},e)=>(0,d.jsx)("div",{ref:e,role:"alert",className:(0,g.cn)(h({variant:b}),a),...c}));i.displayName="Alert",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h5",{ref:c,className:(0,g.cn)("mb-1 font-medium leading-none tracking-tight",a),...b})).displayName="AlertTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,g.cn)("text-sm [&_p]:leading-relaxed",a),...b}));j.displayName="AlertDescription"},94735:a=>{"use strict";a.exports=require("events")},95649:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,generateMetadata:()=>g});var d=c(37413),e=c(52215);async function f({params:a}){let{id:b}=await a;return(0,d.jsx)(e.default,{expertId:b})}async function g({params:a}){let{id:b}=await a;return{title:"Expert Profile - AI Trainer Hub",description:`Connect with AI Expert ${b} for specialized assistance`}}},96882:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97867:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["expert",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,95649)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\expert\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\expert\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/expert/[id]/page",pathname:"/expert/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/expert/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,842,928,235,474,263,127],()=>b(b.s=97867));module.exports=c})();