(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[627],{773:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Z});var r=a(5155),s=a(2115),n=a(5695),l=a(285),i=a(6695),c=a(6126),o=a(6081),d=a(9033),u=a(2712),h=a(3655),m=a(1414);function x(){return()=>{}}var p="Avatar",[g,f]=(0,o.A)(p),[y,v]=g(p),b=s.forwardRef((e,t)=>{let{__scopeAvatar:a,...n}=e,[l,i]=s.useState("idle");return(0,r.jsx)(y,{scope:a,imageLoadingStatus:l,onImageLoadingStatusChange:i,children:(0,r.jsx)(h.sG.span,{...n,ref:t})})});b.displayName=p;var j="AvatarImage",w=s.forwardRef((e,t)=>{let{__scopeAvatar:a,src:n,onLoadingStatusChange:l=()=>{},...i}=e,c=v(j,a),o=function(e,t){let{referrerPolicy:a,crossOrigin:r}=t,n=(0,m.useSyncExternalStore)(x,()=>!0,()=>!1),l=s.useRef(null),i=n?(l.current||(l.current=new window.Image),l.current):null,[c,o]=s.useState(()=>A(i,e));return(0,u.N)(()=>{o(A(i,e))},[i,e]),(0,u.N)(()=>{let e=e=>()=>{o(e)};if(!i)return;let t=e("loaded"),s=e("error");return i.addEventListener("load",t),i.addEventListener("error",s),a&&(i.referrerPolicy=a),"string"==typeof r&&(i.crossOrigin=r),()=>{i.removeEventListener("load",t),i.removeEventListener("error",s)}},[i,r,a]),c}(n,i),p=(0,d.c)(e=>{l(e),c.onImageLoadingStatusChange(e)});return(0,u.N)(()=>{"idle"!==o&&p(o)},[o,p]),"loaded"===o?(0,r.jsx)(h.sG.img,{...i,ref:t,src:n}):null});w.displayName=j;var N="AvatarFallback",k=s.forwardRef((e,t)=>{let{__scopeAvatar:a,delayMs:n,...l}=e,i=v(N,a),[c,o]=s.useState(void 0===n);return s.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>o(!0),n);return()=>window.clearTimeout(e)}},[n]),c&&"loaded"!==i.imageLoadingStatus?(0,r.jsx)(h.sG.span,{...l,ref:t}):null});function A(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}k.displayName=N;var S=a(9434);function C(e){let{className:t,...a}=e;return(0,r.jsx)(b,{"data-slot":"avatar",className:(0,S.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function E(e){let{className:t,...a}=e;return(0,r.jsx)(w,{"data-slot":"avatar-image",className:(0,S.cn)("aspect-square size-full",t),...a})}function F(e){let{className:t,...a}=e;return(0,r.jsx)(k,{"data-slot":"avatar-fallback",className:(0,S.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}var I=a(1154),P=a(5339),z=a(6516),M=a(3786);let T=(0,a(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);var B=a(7580),$=a(8564),L=a(1366),R=a(2138),H=a(7481),_=a(5433),O=a(6874),q=a.n(O);function D(){let e=(0,n.useParams)().shareToken,t=(0,n.useRouter)(),{toast:a}=(0,H.d)(),{loading:o,error:d,shareData:u,loadSharedExpert:h,loadConsent:m,trackClick:x,trackConversion:p}=(0,_._)(e),[g,f]=(0,s.useState)(!1),[y,v]=(0,s.useState)(!1);(0,s.useEffect)(()=>{e&&(h(e),m(e))},[e,h,m]),(0,s.useEffect)(()=>{u&&!g&&(x(e,{source:"direct_link",userAgent:navigator.userAgent,referrer:document.referrer}),f(!0))},[u,g,e,x]);let b=async()=>{if(u)try{if(v(!0),!localStorage.getItem("token")){let e=encodeURIComponent(window.location.href);t.push("/auth/login?returnUrl=".concat(e));return}await p(e,{expertId:u.expert.id,source:"shared_link"}),t.push("/chat/".concat(u.expert.id,"?source=shared&shareToken=").concat(e))}catch(e){console.error("Error starting chat:",e),a({title:"Error",description:"Failed to start chat. Please try again.",variant:"destructive"})}finally{v(!1)}},j=async()=>{try{let e=window.location.href;await navigator.clipboard.writeText(e),a({title:"Link Copied!",description:"Share link copied to clipboard."})}catch(e){a({title:"Error",description:"Failed to copy link. Please copy manually.",variant:"destructive"})}};if(o)return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(I.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading Expert..."}),(0,r.jsx)("p",{className:"text-gray-600",children:"Please wait while we prepare your AI expert."})]})});if(d||!u)return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto p-6",children:[(0,r.jsx)(P.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Expert Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:d||"The shared expert link you're looking for doesn't exist or has been deactivated."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(l.$,{onClick:()=>t.push("/ai-experts"),className:"w-full",children:"Browse All Experts"}),(0,r.jsx)(l.$,{variant:"outline",onClick:()=>t.push("/"),className:"w-full",children:"Go to Homepage"})]})]})});let{expert:w,share:N}=u;return N.isActive?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",children:[(0,r.jsx)("div",{className:"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-10",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(z.A,{className:"h-6 w-6 text-blue-600"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:"Shared AI Expert"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(l.$,{variant:"outline",size:"sm",onClick:j,children:[(0,r.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Share"]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(q(),{href:"/ai-experts",children:[(0,r.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"Browse More"]})})]})]})})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsx)(i.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:(0,r.jsx)(i.Wu,{className:"p-8",children:(0,r.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,r.jsxs)(C,{className:"h-20 w-20 border-4 border-white shadow-lg",children:[(0,r.jsx)(E,{src:w.imageUrl,alt:w.name}),(0,r.jsx)(F,{className:"text-2xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white",children:w.name.charAt(0)})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:w.name}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:w.description}),w.tags&&w.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:w.tags.map((e,t)=>(0,r.jsxs)(c.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:[(0,r.jsx)(T,{className:"h-3 w-3 mr-1"}),e]},t))}),w.category&&(0,r.jsx)(c.E,{variant:"outline",className:"mb-4",children:w.category}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)(B.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Created by ",w.createdBy.displayName||w.createdBy.username]})]})]})]})})}),(0,r.jsxs)(i.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)($.A,{className:"h-5 w-5 text-yellow-500"}),(0,r.jsx)("span",{children:"Share Statistics"})]})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:N.clickCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Total Views"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:new Date(N.createdAt).toLocaleDateString()}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Shared On"})]})]})})]}),(0,r.jsxs)(i.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"What You Can Do"}),(0,r.jsx)(i.BT,{children:"This AI expert can help you with specialized knowledge and assistance."})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(L.A,{className:"h-5 w-5 text-blue-500 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Start a Conversation"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Chat directly with this AI expert to get personalized assistance."})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)($.A,{className:"h-5 w-5 text-yellow-500 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Get Expert Advice"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Receive specialized knowledge and insights tailored to your needs."})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(B.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Join the Community"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Discover more AI experts and expand your knowledge network."})]})]})]})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(i.Zp,{className:"border-0 shadow-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white",children:(0,r.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,r.jsx)(L.A,{className:"h-12 w-12 mx-auto mb-4 opacity-90"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Start Chatting Now"}),(0,r.jsxs)("p",{className:"text-blue-100 mb-6 text-sm",children:["Begin your conversation with ",w.name," and get the help you need."]}),(0,r.jsx)(l.$,{onClick:b,disabled:y,className:"w-full bg-white text-blue-600 hover:bg-gray-100 font-semibold",size:"lg",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(I.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Starting Chat..."]}):(0,r.jsxs)(r.Fragment,{children:["Start Chat",(0,r.jsx)(R.A,{className:"h-4 w-4 ml-2"})]})})]})}),(0,r.jsxs)(i.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{className:"text-lg",children:"Share Information"})}),(0,r.jsx)(i.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Monitoring"}),(0,r.jsx)(c.E,{variant:N.monitorEnabled?"default":"secondary",children:N.monitorEnabled?"Enabled":"Disabled"})]})})]}),(0,r.jsxs)(i.Zp,{className:"border-0 shadow-lg bg-white/90 backdrop-blur-sm",children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{className:"text-lg",children:"Explore More"}),(0,r.jsx)(i.BT,{children:"Discover other AI experts that might interest you."})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(l.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,r.jsxs)(q(),{href:"/ai-experts",children:[(0,r.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Browse All Experts"]})}),(0,r.jsx)(l.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,r.jsxs)(q(),{href:"/auth/register",children:[(0,r.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Create Your Own Expert"]})})]})})]})]})]})}),(0,r.jsx)("div",{className:"bg-white/80 backdrop-blur-sm border-t mt-12",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-6 text-center",children:(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:["Powered by ",(0,r.jsx)("span",{className:"font-semibold text-blue-600",children:"AI Trainer Hub"})," - Your marketplace for AI expertise"]})})})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto p-6",children:[(0,r.jsx)(P.A,{className:"h-16 w-16 text-gray-500 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Link Inactive"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"This shared expert link has been deactivated by the creator."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(l.$,{onClick:()=>t.push("/ai-experts"),className:"w-full",children:"Browse Available Experts"}),(0,r.jsx)(l.$,{variant:"outline",onClick:()=>t.push("/"),className:"w-full",children:"Go to Homepage"})]})]})})}function Z(){return(0,r.jsx)(D,{})}},1154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1366:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1414:(e,t,a)=>{"use strict";e.exports=a(2436)},2138:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2436:(e,t,a)=>{"use strict";var r=a(2115),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=r.useState,l=r.useEffect,i=r.useLayoutEffect,c=r.useDebugValue;function o(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!s(e,a)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var a=t(),r=n({inst:{value:a,getSnapshot:t}}),s=r[0].inst,d=r[1];return i(function(){s.value=a,s.getSnapshot=t,o(s)&&d({inst:s})},[e,a,t]),l(function(){return o(s)&&d({inst:s}),e(function(){o(s)&&d({inst:s})})},[e]),c(a),a};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},2712:(e,t,a)=>{"use strict";a.d(t,{N:()=>s});var r=a(2115),s=globalThis?.document?r.useLayoutEffect:()=>{}},3655:(e,t,a)=>{"use strict";a.d(t,{hO:()=>c,sG:()=>i});var r=a(2115),s=a(7650),n=a(9708),l=a(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=(0,n.TL)(`Primitive.${t}`),s=r.forwardRef((e,r)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s?a:t,{...n,ref:r})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function c(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},3786:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},5339:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5433:(e,t,a)=>{"use strict";a.d(t,{E:()=>l,_:()=>i});var r=a(2115),s=a(7481),n=a(5731);function l(){let{toast:e}=(0,s.d)(),[t,a]=(0,r.useState)(!1),[l,i]=(0,r.useState)(null),c=(0,r.useCallback)(async t=>{try{a(!0),i(null);let r=await n.FH.post("/sharing/shares",{body:t,headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(r.data.success){let t=r.data.data,a="".concat(window.location.origin,"/shared/").concat(t.shareToken);return e({title:"Share Created!",description:"Your expert share link has been created successfully."}),{...t,shareUrl:a}}throw Error(r.data.message||"Failed to create share")}catch(a){var r,s;let t=(null==(s=a.response)||null==(r=s.data)?void 0:r.message)||a.message||"Failed to create share";return i(t),e({title:"Error",description:t,variant:"destructive"}),null}finally{a(!1)}},[e]),o=(0,r.useCallback)(async t=>{try{a(!0),i(null);let e=await n.FH.get("/sharing/shared/".concat(t));if(e.data.success)return e.data.data;throw Error(e.data.message||"Failed to load shared expert")}catch(a){var r,s,l;let t=(null==(s=a.response)||null==(r=s.data)?void 0:r.message)||a.message||"Failed to load shared expert";return i(t),(null==(l=a.response)?void 0:l.status)!==404&&e({title:"Error",description:t,variant:"destructive"}),null}finally{a(!1)}},[e]),d=(0,r.useCallback)(async(e,t)=>{try{return(await n.FH.post("/sharing/track/".concat(e,"/click"),{body:{metadata:{userAgent:navigator.userAgent,referrer:document.referrer,timestamp:new Date().toISOString(),...t}}})).data.success}catch(e){return console.error("Failed to track click:",e),!1}},[]),u=(0,r.useCallback)(async(e,t)=>{try{return(await n.FH.post("/sharing/track/".concat(e,"/conversion"),{body:{metadata:{timestamp:new Date().toISOString(),...t}},headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}})).data.success}catch(e){return console.error("Failed to track conversion:",e),!1}},[]),h=(0,r.useCallback)(async e=>{try{let t=await n.FH.get("/sharing/consent/".concat(e));if(t.data.success)return t.data.data;return null}catch(e){return console.error("Failed to get consent:",e),null}},[]),m=(0,r.useCallback)(async function(t,a){let r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{if((await n.FH.post("/sharing/consent/".concat(t),{body:{consent:a,trackingEnabled:r}})).data.success)return e({title:a?"Consent Granted":"Consent Withdrawn",description:a?"Thank you for allowing us to improve your experience.":"Your privacy preferences have been updated."}),!0;return!1}catch(t){return console.error("Failed to set consent:",t),e({title:"Error",description:"Failed to update consent preferences.",variant:"destructive"}),!1}},[e]),x=(0,r.useCallback)(async(t,r)=>{try{a(!0),i(null);let s=await n.FH.put("/sharing/shares/".concat(t),{body:r,headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(s.data.success)return e({title:"Share Updated",description:"Your share settings have been updated successfully."}),!0;throw Error(s.data.message||"Failed to update share")}catch(a){var s,l;let t=(null==(l=a.response)||null==(s=l.data)?void 0:s.message)||a.message||"Failed to update share";return i(t),e({title:"Error",description:t,variant:"destructive"}),!1}finally{a(!1)}},[e]),p=(0,r.useCallback)(async t=>{try{a(!0),i(null);let r=await n.FH.delete("/sharing/shares/".concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(r.data.success)return e({title:"Share Deleted",description:"The share link has been deactivated successfully."}),!0;throw Error(r.data.message||"Failed to delete share")}catch(a){var r,s;let t=(null==(s=a.response)||null==(r=s.data)?void 0:r.message)||a.message||"Failed to delete share";return i(t),e({title:"Error",description:t,variant:"destructive"}),!1}finally{a(!1)}},[e]),g=(0,r.useCallback)(async()=>{try{a(!0),i(null);let e=await n.FH.get("/sharing/shares/my",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.data.success)return e.data.data;throw Error(e.data.message||"Failed to load shares")}catch(s){var t,r;let a=(null==(r=s.response)||null==(t=r.data)?void 0:t.message)||s.message||"Failed to load shares";return i(a),e({title:"Error",description:a,variant:"destructive"}),null}finally{a(!1)}},[e]),f=(0,r.useCallback)(async function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"7d";try{a(!0),i(null);let e=await n.FH.get("/sharing/analytics/".concat(t,"?timeRange=").concat(r),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.data.success)return e.data.data;throw Error(e.data.message||"Failed to load analytics")}catch(a){var s,l;let t=(null==(l=a.response)||null==(s=l.data)?void 0:s.message)||a.message||"Failed to load analytics";return i(t),e({title:"Error",description:t,variant:"destructive"}),null}finally{a(!1)}},[e]),y=(0,r.useCallback)(async t=>{try{let a="".concat(window.location.origin,"/shared/").concat(t);return await navigator.clipboard.writeText(a),e({title:"Copied!",description:"Share link copied to clipboard."}),!0}catch(t){return console.error("Failed to copy to clipboard:",t),e({title:"Error",description:"Failed to copy link. Please copy manually.",variant:"destructive"}),!1}},[e]);return{loading:t,error:l,createShare:c,getSharedExpert:o,trackClick:d,trackConversion:u,getConsent:h,setConsent:m,updateShare:x,deleteShare:p,getUserShares:g,getAnalytics:f,copyShareUrl:y,generateShareUrl:(0,r.useCallback)(e=>"".concat(window.location.origin,"/shared/").concat(e),[]),clearError:(0,r.useCallback)(()=>{i(null)},[])}}function i(e){let[t,a]=(0,r.useState)(e||null),[s,n]=(0,r.useState)(null),[i,c]=(0,r.useState)(null),o=l(),d=(0,r.useCallback)(async e=>{a(e);let t=await o.getSharedExpert(e);return n(t),t},[o]);return{shareToken:t,shareData:s,consentData:i,loadSharedExpert:d,loadConsent:(0,r.useCallback)(async e=>{let t=await o.getConsent(e);return c(t),t},[o]),handleConsent:(0,r.useCallback)(async function(e,t){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=await o.setConsent(e,t,a);return r&&c({hasConsented:t,consentTimestamp:new Date().toISOString(),trackingEnabled:a}),r},[o]),...o}}},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},6081:(e,t,a)=>{"use strict";a.d(t,{A:()=>l,q:()=>n});var r=a(2115),s=a(5155);function n(e,t){let a=r.createContext(t),n=e=>{let{children:t,...n}=e,l=r.useMemo(()=>n,Object.values(n));return(0,s.jsx)(a.Provider,{value:l,children:t})};return n.displayName=e+"Provider",[n,function(s){let n=r.useContext(a);if(n)return n;if(void 0!==t)return t;throw Error(`\`${s}\` must be used within \`${e}\``)}]}function l(e,t=[]){let a=[],n=()=>{let t=a.map(e=>r.createContext(e));return function(a){let s=a?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...a,[e]:s}}),[a,s])}};return n.scopeName=e,[function(t,n){let l=r.createContext(n),i=a.length;a=[...a,n];let c=t=>{let{scope:a,children:n,...c}=t,o=a?.[e]?.[i]||l,d=r.useMemo(()=>c,Object.values(c));return(0,s.jsx)(o.Provider,{value:d,children:n})};return c.displayName=t+"Provider",[c,function(a,s){let c=s?.[e]?.[i]||l,o=r.useContext(c);if(o)return o;if(void 0!==n)return n;throw Error(`\`${a}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let a=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=a.reduce((t,{useScope:a,scopeName:r})=>{let s=a(e)[`__scope${r}`];return{...t,...s}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return a.scopeName=t.scopeName,a}(n,...t)]}},6126:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var r=a(5155);a(2115);var s=a(2085),n=a(9434);let l=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...s}=e;return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:a}),t),...s})}},6516:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},6712:(e,t,a)=>{Promise.resolve().then(a.bind(a,773))},7481:(e,t,a)=>{"use strict";a.d(t,{d:()=>c});var r=a(2115);let s={toasts:[]},n=[];function l(){n.forEach(e=>e(s))}function i(e){let t=s.toasts.findIndex(t=>t.id===e);t>-1&&(s.toasts.splice(t,1),l())}function c(){let[e,t]=(0,r.useState)(s),a=(0,r.useCallback)(e=>(n.push(e),()=>{n=n.filter(t=>t!==e)}),[]),c=(0,r.useCallback)(e=>(function(e){let t=Math.random().toString(36).substr(2,9),a={id:t,duration:5e3,...e};return s.toasts.push(a),l(),a.duration&&a.duration>0&&setTimeout(()=>{i(t)},a.duration),t})(e),[]),o=(0,r.useCallback)(e=>{i(e)},[]);return(0,r.useState)(()=>a(t)),{toast:c,dismiss:o,toasts:e.toasts}}},7580:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8564:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9033:(e,t,a)=>{"use strict";a.d(t,{c:()=>s});var r=a(2115);function s(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}}},e=>{e.O(0,[445,874,352,573,441,964,358],()=>e(e.s=6712)),_N_E=e.O()}]);