(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[916],{1154:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3109:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3311:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},3717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3760:(e,s,t)=>{Promise.resolve().then(t.bind(t,9622))},4165:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>m,L3:()=>u,c7:()=>x,lG:()=>i,rr:()=>p,zM:()=>d});var a=t(5155);t(2115);var l=t(3470),r=t(4416),n=t(9434);function i(e){let{...s}=e;return(0,a.jsx)(l.bL,{"data-slot":"dialog",...s})}function d(e){let{...s}=e;return(0,a.jsx)(l.l9,{"data-slot":"dialog-trigger",...s})}function c(e){let{...s}=e;return(0,a.jsx)(l.ZL,{"data-slot":"dialog-portal",...s})}function o(e){let{className:s,...t}=e;return(0,a.jsx)(l.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...t})}function m(e){let{className:s,children:t,showCloseButton:i=!0,...d}=e;return(0,a.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,a.jsx)(o,{}),(0,a.jsxs)(l.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...d,children:[t,i&&(0,a.jsxs)(l.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(r.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",s),...t})}function u(e){let{className:s,...t}=e;return(0,a.jsx)(l.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",s),...t})}function p(e){let{className:s,...t}=e;return(0,a.jsx)(l.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",s),...t})}},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8564:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9376:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},9622:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>q});var a=t(5155),l=t(5695),r=t(2115),n=t(6766),i=t(5731),d=t(1154),c=t(9946);let o=(0,c.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var m=t(3311);let x=e=>{var s,t,l,c,x,u;let{onExpertCreated:p,onCancel:h}=e,[g,b]=(0,r.useState)({name:"",description:"",systemPrompt:"",model:"gpt-4o-mini",pricingPercentage:"0.00",isPublic:!1}),[j,f]=(0,r.useState)([]),[y,v]=(0,r.useState)(""),[N,w]=(0,r.useState)(null),[k,C]=(0,r.useState)(null),[A,P]=(0,r.useState)(!1),[M,S]=(0,r.useState)(null),[D,F]=(0,r.useState)(!1),[E,I]=(0,r.useState)(!1),[G,L]=(0,r.useState)(null),[U,T]=(0,r.useState)(null),[R,z]=(0,r.useState)({quality:"standard",size:"1024x1024",style:"natural"}),O=()=>{var e,s,t,a,l;if(!(null==U?void 0:U.imageGeneration))return null;let{quality:r,size:n}=R;return"low"===r?(null==(e=U.imageGeneration.low)?void 0:e.estimated)||null:"standard"===r?"1024x1024"===n?(null==(s=U.imageGeneration.standard)?void 0:s.estimated)||null:(null==(t=U.imageGeneration.standardWide)?void 0:t.estimated)||null:"high"===r?"1024x1024"===n?(null==(a=U.imageGeneration.high)?void 0:a.estimated)||null:(null==(l=U.imageGeneration.highWide)?void 0:l.estimated)||null:null};r.useEffect(()=>{(async()=>{try{let e=await i.FH.get("/api/ai-generation/costs");e.success&&T(e.costs)}catch(e){console.error("Failed to load AI costs:",e)}})()},[]);let H=e=>{let{name:s,value:t,type:a,checked:l}=e.target;b(e=>({...e,[s]:"checkbox"===a?l:t}))},Z=()=>{y.trim()&&j.length<5&&!j.includes(y.trim())&&(f(e=>[...e,y.trim()]),v(""))},q=async()=>{if(!g.name||!g.systemPrompt)return void S("Please fill in the name and system prompt before generating labels.");F(!0),S(null);try{let e=await i.FH.post("/api/ai-generation/labels",{body:{name:g.name,description:g.description,systemPrompt:g.systemPrompt,model:g.model}});e.success&&(f(e.labels),console.log("Labels generated successfully! Cost: ".concat(e.cost," IDR")))}catch(a){var e,s,t;console.error("Label generation error:",a),(null==(e=a.response)?void 0:e.status)===402?S("Insufficient balance for AI label generation. Please top up your account."):S((null==(t=a.response)||null==(s=t.data)?void 0:s.message)||"Failed to generate labels. Please try again.")}finally{F(!1)}},B=async()=>{if(!g.name)return void S("Please fill in the expert name before generating an image.");I(!0),S(null);try{let e=await i.FH.post("/api/ai-generation/image",{body:{name:g.name,description:g.description,labels:j,quality:R.quality,size:R.size,style:R.style}});e.success&&(L(e.imageUrl),T(s=>({...s,imageGeneration:{...null==s?void 0:s.imageGeneration,actual:e.cost}})),console.log("Image generated successfully with ".concat(e.model,"! Cost: ").concat(e.cost," IDR")),e.fallback&&S("Content policy violation detected. Using default image instead."))}catch(a){var e,s,t;console.error("Image generation error:",a),(null==(e=a.response)?void 0:e.status)===402?S("Insufficient balance for AI image generation. Please top up your account."):S((null==(t=a.response)||null==(s=t.data)?void 0:s.message)||"Failed to generate image. Please try again.")}finally{I(!1)}},J=async e=>{e.preventDefault(),P(!0),S(null);try{let e=new FormData;e.append("name",g.name),e.append("description",g.description),e.append("systemPrompt",g.systemPrompt),e.append("model",g.model),e.append("pricingPercentage",g.pricingPercentage),e.append("isPublic",g.isPublic.toString()),e.append("labels",JSON.stringify(j)),N&&e.append("file",N),k&&e.append("image",k);let s=await i.FH.createExpert(e);s.success?(null==p||p(s.expert),b({name:"",description:"",systemPrompt:"",model:"gpt-4o-mini",pricingPercentage:"0.00",isPublic:!1}),f([]),v(""),w(null),C(null)):S(s.error||"Failed to create expert")}catch(e){S(e.message||"Failed to create expert")}finally{P(!1)}};return(0,a.jsxs)("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-center mb-6",children:"Create AI Expert"}),(0,a.jsx)("p",{className:"text-gray-600 text-center mb-8",children:"Fill in the details to create a new AI expert profile."}),M&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:M}),(0,a.jsxs)("form",{onSubmit:J,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:g.name,onChange:H,placeholder:"e.g., Creative Writer",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,a.jsx)("textarea",{id:"description",name:"description",value:g.description,onChange:H,placeholder:"Describe the expert's capabilities and purpose.",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("label",{htmlFor:"imageFile",className:"block text-sm font-medium text-gray-700",children:"Upload Image (Optional)"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:R.quality,onChange:e=>z(s=>({...s,quality:e.target.value})),className:"text-xs px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500",disabled:E,children:[(0,a.jsx)("option",{value:"low",children:"Low Quality (272 tokens)"}),(0,a.jsx)("option",{value:"standard",children:"Standard (1360 tokens)"}),(0,a.jsx)("option",{value:"high",children:"High Quality (2720 tokens)"})]}),(0,a.jsxs)("select",{value:R.size,onChange:e=>z(s=>({...s,size:e.target.value})),className:"text-xs px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500",disabled:E,children:[(0,a.jsx)("option",{value:"1024x1024",children:"1024\xd71024"}),(0,a.jsx)("option",{value:"1024x1536",children:"1024\xd71536"}),(0,a.jsx)("option",{value:"1536x1024",children:"1536\xd71024"})]}),(0,a.jsx)("button",{type:"button",onClick:B,disabled:E||!g.name,className:"inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed",children:E?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-3 h-3 mr-1 animate-spin"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o,{className:"w-3 h-3 mr-1"}),"GPT-Image-1",O()&&(0,a.jsxs)("span",{className:"ml-1 text-gray-500",children:["(~",O()," IDR)"]})]})})]})]}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[(0,a.jsx)("input",{type:"file",id:"imageFile",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){let e=t.name.toLowerCase().substring(t.name.lastIndexOf("."));if(t.size>0xa00000){S("Image file size must be less than 10MB"),C(null);return}["image/png","image/jpeg","image/jpg","image/gif","image/webp"].includes(t.type)||[".png",".jpg",".jpeg",".gif",".webp"].includes(e)?(C(t),S(null)):(S("Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP"),C(null))}},accept:".png,.jpg,.jpeg,.gif,.webp",className:"hidden"}),(0,a.jsxs)("label",{htmlFor:"imageFile",className:"cursor-pointer",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-2",children:(0,a.jsx)("svg",{className:"mx-auto h-12 w-12",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,a.jsx)("path",{d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20v-2a2 2 0 012-2h8a2 2 0 012 2v2m0 0v8a2 2 0 01-2 2H8a2 2 0 01-2-2v-8m8-2V8a2 2 0 00-2-2H8a2 2 0 00-2 2v2m0 0h12m0 0v8a2 2 0 01-2 2h-8a2 2 0 01-2-2v-8z",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,a.jsx)("p",{className:"text-blue-600 hover:text-blue-500",children:"Upload an image or drag and drop"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"PNG, JPG, GIF, WEBP up to 10MB"})]}),k&&(0,a.jsxs)("p",{className:"mt-2 text-sm text-green-600",children:["Selected: ",k.name]})]}),G&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-purple-900",children:"AI Generated Image"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{G&&fetch(G).then(e=>e.blob()).then(e=>{C(new File([e],"ai-generated-image.png",{type:"image/png"})),L(null)}).catch(e=>{console.error("Failed to use generated image:",e),S("Failed to use generated image. Please try again.")})},className:"px-3 py-1 text-xs font-medium text-white bg-purple-600 rounded hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500",children:"Use This Image"}),(0,a.jsx)("button",{type:"button",onClick:()=>L(null),className:"px-3 py-1 text-xs font-medium text-purple-600 bg-white border border-purple-300 rounded hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-purple-500",children:"Discard"})]})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(n.default,{src:G,alt:"AI Generated Expert Image",width:300,height:192,className:"max-w-xs max-h-48 rounded-lg shadow-md object-cover"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"knowledgeBaseFile",className:"block text-sm font-medium text-gray-700 mb-2",children:"Upload Knowledge Base (Optional)"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[(0,a.jsx)("input",{type:"file",id:"knowledgeBaseFile",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){let e=t.name.toLowerCase().substring(t.name.lastIndexOf("."));["application/pdf","text/plain","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/markdown","application/json"].includes(t.type)||[".pdf",".txt",".docx",".doc",".md",".json"].includes(e)?(w(t),S(null)):(S("Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON"),w(null))}},accept:".pdf,.txt,.docx,.doc,.md,.json",className:"hidden"}),(0,a.jsxs)("label",{htmlFor:"knowledgeBaseFile",className:"cursor-pointer",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-2",children:(0,a.jsx)("svg",{className:"mx-auto h-12 w-12",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,a.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,a.jsx)("p",{className:"text-blue-600 hover:text-blue-500",children:"Upload a knowledge base file or drag and drop"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"PDF, TXT, DOCX, DOC, MD, JSON"})]}),N&&(0,a.jsxs)("p",{className:"mt-2 text-sm text-green-600",children:["Selected: ",N.name]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"systemPrompt",className:"block text-sm font-medium text-gray-700 mb-2",children:"System Prompt"}),(0,a.jsx)("textarea",{id:"systemPrompt",name:"systemPrompt",value:g.systemPrompt,onChange:H,placeholder:"Enter the system prompt that defines the expert's behavior.",rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"model",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Selection"}),(0,a.jsx)("select",{id:"model",name:"model",value:g.model,onChange:H,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"gpt-3.5-turbo",label:"GPT-3.5 Turbo"},{value:"gpt-4",label:"GPT-4"},{value:"gpt-4-turbo",label:"GPT-4 Turbo"},{value:"gpt-4o",label:"GPT-4o"},{value:"gpt-4o-mini",label:"GPT-4o Mini"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricingPercentage",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pricing (% of token usage)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"number",id:"pricingPercentage",name:"pricingPercentage",value:g.pricingPercentage,onChange:H,min:"0",max:"100",step:"0.01",className:"w-full px-3 py-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"absolute right-3 top-2 text-gray-500",children:"%"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",name:"isPublic",checked:g.isPublic,onChange:H,className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Make this expert public (others can discover and use it)"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1 ml-7",children:g.isPublic?"This expert will be visible to all users":"This expert will be private (unlisted)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Labels (max 5)"}),(0,a.jsx)("button",{type:"button",onClick:q,disabled:D||!g.name||!g.systemPrompt,className:"inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:D?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-3 h-3 mr-1 animate-spin"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"AI Generate",(null==U?void 0:U.labelGeneration)&&(0,a.jsxs)("span",{className:"ml-1 text-gray-500",children:["(~",U.labelGeneration.estimated," IDR)"]})]})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:y,onChange:e=>v(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),Z())},placeholder:"Add a label...",maxLength:50,className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:j.length>=5}),(0,a.jsx)("button",{type:"button",onClick:Z,disabled:!y.trim()||j.length>=5||j.includes(y.trim()),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Add"})]}),j.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:j.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>{f(j.filter((e,t)=>t!==s))},className:"ml-2 text-blue-600 hover:text-blue-800 focus:outline-none",children:"\xd7"})]},s))}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[j.length,"/5 labels used. Labels help users discover your expert."]})]})]}),((null==U||null==(s=U.labelGeneration)?void 0:s.actual)||(null==U||null==(t=U.imageGeneration)?void 0:t.actual))&&(0,a.jsxs)("div",{className:"p-4 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"AI Generation Costs Used"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(null==(l=U.labelGeneration)?void 0:l.actual)&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Label Generation:"}),(0,a.jsxs)("span",{className:"font-medium",children:[U.labelGeneration.actual," IDR"]})]}),(null==(c=U.imageGeneration)?void 0:c.actual)&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Image Generation:"}),(0,a.jsxs)("span",{className:"font-medium",children:[U.imageGeneration.actual," IDR"]})]}),(0,a.jsx)("div",{className:"border-t border-gray-300 pt-1 mt-2",children:(0,a.jsxs)("div",{className:"flex justify-between font-medium text-gray-900",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsxs)("span",{children:[((null==(x=U.labelGeneration)?void 0:x.actual)||0)+((null==(u=U.imageGeneration)?void 0:u.actual)||0)," IDR"]})]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4",children:[h&&(0,a.jsx)("button",{type:"button",onClick:h,className:"px-6 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:A,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:A?"Creating...":"Create Expert"})]})]})]})};var u=t(9434);let p=e=>{let{expert:s,onExpertUpdated:t,onCancel:l}=e,[d,c]=(0,r.useState)({name:s.name,description:s.description||"",systemPrompt:s.systemPrompt,model:s.model,pricingPercentage:s.pricingPercentage.toString(),isPublic:s.isPublic||!1}),[o,m]=(0,r.useState)(s.labels||[]),[x,p]=(0,r.useState)(""),[h,g]=(0,r.useState)(null),[b,j]=(0,r.useState)(null),[f,y]=(0,r.useState)(!1),[v,N]=(0,r.useState)(null),w=e=>{let{name:s,value:t,type:a,checked:l}=e.target;c(e=>({...e,[s]:"checkbox"===a?l:t}))},k=()=>{x.trim()&&o.length<5&&!o.includes(x.trim())&&(m(e=>[...e,x.trim()]),p(""))},C=async e=>{if(e.preventDefault(),!d.name.trim()||!d.systemPrompt.trim())return void N("Name and system prompt are required");let a=parseFloat(d.pricingPercentage);if(isNaN(a)||a<0||a>100)return void N("Pricing percentage must be between 0 and 100");y(!0),N(null);try{let e=await i.FH.updateExpert(s.id,{name:d.name.trim(),description:d.description.trim(),systemPrompt:d.systemPrompt.trim(),model:d.model,pricingPercentage:a,isPublic:d.isPublic,labels:o},h,b);e.success?null==t||t(e.expert):N(e.error||"Failed to update expert")}catch(e){N(e.message||"Failed to update expert")}finally{y(!1)}};return(0,a.jsxs)("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Edit AI Expert"}),v&&(0,a.jsx)("div",{className:"mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded",children:v}),(0,a.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Expert Name *"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:d.name,onChange:w,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter expert name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,a.jsx)("textarea",{id:"description",name:"description",value:d.description,onChange:w,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe what this expert does"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"systemPrompt",className:"block text-sm font-medium text-gray-700 mb-2",children:"System Prompt *"}),(0,a.jsx)("textarea",{id:"systemPrompt",name:"systemPrompt",value:d.systemPrompt,onChange:w,required:!0,rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Define the expert's behavior and knowledge"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"model",className:"block text-sm font-medium text-gray-700 mb-2",children:"AI Model"}),(0,a.jsx)("select",{id:"model",name:"model",value:d.model,onChange:w,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"gpt-3.5-turbo",label:"GPT-3.5 Turbo"},{value:"gpt-4",label:"GPT-4"},{value:"gpt-4-turbo",label:"GPT-4 Turbo"},{value:"gpt-4o",label:"GPT-4o"},{value:"gpt-4o-mini",label:"GPT-4o Mini"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricingPercentage",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pricing Percentage (%)"}),(0,a.jsx)("input",{type:"number",id:"pricingPercentage",name:"pricingPercentage",value:d.pricingPercentage,onChange:w,min:"0",max:"100",step:"0.01",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0.00"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",name:"isPublic",checked:d.isPublic,onChange:w,className:"rounded border-gray-300 focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Make this expert public"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Public experts can be discovered and used by other users"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"knowledgeBase",className:"block text-sm font-medium text-gray-700 mb-2",children:"Knowledge Base File (Optional)"}),(0,a.jsx)("input",{type:"file",id:"knowledgeBase",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){let e=t.name.toLowerCase().substring(t.name.lastIndexOf("."));["application/pdf","text/plain","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/markdown","application/json"].includes(t.type)||[".pdf",".txt",".docx",".doc",".md",".json"].includes(e)?(g(t),N(null)):(N("Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON"),g(null))}},accept:".pdf,.txt,.docx,.doc,.md,.json",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Upload a new file to update the knowledge base. Supported: PDF, TXT, DOCX, DOC, MD, JSON"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"image",className:"block text-sm font-medium text-gray-700 mb-2",children:"Expert Image (Optional)"}),(0,a.jsx)("input",{type:"file",id:"image",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){let e=t.name.toLowerCase().substring(t.name.lastIndexOf("."));if(t.size>0xa00000){N("Image file size must be less than 10MB"),j(null);return}["image/png","image/jpeg","image/jpg","image/gif","image/webp"].includes(t.type)||[".png",".jpg",".jpeg",".gif",".webp"].includes(e)?(j(t),N(null)):(N("Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP"),j(null))}},accept:"image/*",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Upload a new image to replace the current one. Max 10MB. Supported: PNG, JPG, JPEG, GIF, WEBP"}),s.imageUrl&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Current image:"}),(0,a.jsx)(n.default,{src:(0,u.L)(s.imageUrl),alt:"Current expert image",width:64,height:64,className:"w-16 h-16 object-cover rounded border mt-1"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Labels (Optional)"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:x,onChange:e=>p(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),k())},maxLength:50,className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Add a label (max 50 characters)"}),(0,a.jsx)("button",{type:"button",onClick:k,disabled:!x.trim()||o.length>=5||o.includes(x.trim()),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Add"})]}),o.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:o.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>{m(e=>e.filter((e,t)=>t!==s))},className:"ml-2 text-blue-600 hover:text-blue-800 focus:outline-none",children:"\xd7"})]},s))}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[o.length,"/5 labels used. Labels help users discover your expert."]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4",children:[l&&(0,a.jsx)("button",{type:"button",onClick:l,className:"px-6 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:f,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Updating...":"Update Expert"})]})]})]})};var h=t(285),g=t(6695),b=t(4165),j=t(4616),f=t(3717),y=t(2657),v=t(3109),N=t(7580),w=t(5868),k=t(9074);let C=(0,c.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var A=t(2713);let P=(0,c.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var M=t(4186),S=t(6874),D=t.n(S);let F=e=>{let{onExpertEdit:s,refreshTrigger:t,onCreateExpert:l}=e,[c,o]=(0,r.useState)([]),[m,x]=(0,r.useState)(!0),[p,S]=(0,r.useState)(null),[F,E]=(0,r.useState)({}),[I,G]=(0,r.useState)(!1),[L,U]=(0,r.useState)(null),T=async()=>{try{x(!0),S(null);let e=await i.FH.listExperts();e.success?(o(e.experts),await R(e.experts)):S(e.error||"Failed to load experts")}catch(e){S(e.message||"Failed to load experts")}finally{x(!1)}},R=async e=>{let s={};await Promise.all(e.map(async e=>{try{let t=await i.FH.getExpertStats(e.id.toString());t.success?s[e.id]={totalUsers:t.stats.totalUsers,totalCommission:t.stats.totalCommission,last30DaysCommission:t.stats.last30DaysCommission,totalSessions:t.stats.totalSessions,totalMessages:t.stats.totalMessages}:s[e.id]={totalUsers:0,totalCommission:0,last30DaysCommission:0,totalSessions:0,totalMessages:0}}catch(t){console.error("Failed to load stats for expert ".concat(e.id,":"),t),s[e.id]={totalUsers:0,totalCommission:0,last30DaysCommission:0,totalSessions:0,totalMessages:0}}})),E(s)};(0,r.useEffect)(()=>{T()},[t]);let z=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),O=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),H=e=>e?e.includes("business")||e.includes("marketing")?"\uD83D\uDCBC":e.includes("code")||e.includes("programming")?"\uD83D\uDCBB":e.includes("creative")||e.includes("design")?"\uD83C\uDFA8":e.includes("education")||e.includes("learning")?"\uD83D\uDCDA":e.includes("health")||e.includes("medical")?"\uD83C\uDFE5":e.includes("finance")||e.includes("money")?"\uD83D\uDCB0":"\uD83E\uDD16":"\uD83E\uDD16";return m?(0,a.jsxs)("div",{className:"flex justify-center items-center p-12",children:[(0,a.jsx)(d.A,{className:"w-8 h-8 animate-spin text-blue-600"}),(0,a.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading experts..."})]}):p?(0,a.jsx)(g.Zp,{className:"p-6 border-red-200 bg-red-50",children:(0,a.jsxs)("div",{className:"text-red-700",children:[(0,a.jsx)("p",{className:"font-semibold mb-2",children:"Error"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:p}),(0,a.jsx)(h.$,{onClick:T,variant:"outline",className:"border-red-300 text-red-700 hover:bg-red-100",children:"Retry"})]})}):0===c.length?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:"Your AI Experts (0)"}),l&&(0,a.jsxs)(h.$,{onClick:l,className:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-lg flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"w-5 h-5"}),"Create Expert"]})]}),(0,a.jsx)(g.Zp,{className:"p-12 text-center border-dashed border-2 border-gray-300",children:(0,a.jsxs)("div",{className:"text-gray-500",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD16"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"No experts created yet"}),(0,a.jsx)("p",{className:"text-sm",children:"Create your first AI expert to get started!"})]})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"text-2xl font-bold text-gray-900",children:["Your AI Experts (",c.length,")"]}),l&&(0,a.jsxs)(h.$,{onClick:l,className:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-lg flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"w-5 h-5"}),"Create Expert"]})]}),(0,a.jsx)("div",{className:"grid gap-6",children:c.map(e=>{let t=F[e.id];return(0,a.jsx)(g.Zp,{className:"bg-white shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-200",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e.imageUrl?(0,a.jsx)(n.default,{src:(0,u.L)(e.imageUrl),alt:e.name,width:64,height:64,className:"w-16 h-16 object-cover rounded-full border-3 border-white shadow-lg"}):(0,a.jsx)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg",style:{backgroundColor:"#1E3A8A"},children:H(e.labels)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("h4",{className:"text-xl font-bold text-gray-900",children:e.name}),(0,a.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium",children:e.model}),e.isPublic&&(0,a.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium",children:"Public"})]}),e.description&&(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:e.description}),e.labels&&e.labels.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.labels.map((e,s)=>(0,a.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(h.$,{onClick:()=>null==s?void 0:s(e),variant:"outline",size:"sm",className:"border-orange-200 text-orange-700 hover:bg-orange-50",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-1"}),"Edit"]}),(0,a.jsx)(D(),{href:"/expert/".concat(e.id),children:(0,a.jsxs)(h.$,{size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 mr-1"}),"View"]})})]})]}),t&&(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 mb-4",children:[(0,a.jsxs)("h5",{className:"font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-2 text-blue-600"}),"Performance Stats"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(N.A,{className:"w-4 h-4 text-blue-600 mr-1"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-blue-600",children:t.totalUsers})]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Total Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(w.A,{className:"w-4 h-4 text-green-600 mr-1"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-green-600",children:z(t.totalCommission).replace("IDR","").trim()})]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Total Commission"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 text-orange-600 mr-1"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-orange-600",children:z(t.last30DaysCommission).replace("IDR","").trim()})]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Last 30 Days"})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(h.$,{onClick:()=>(e=>{let s=c.find(s=>s.id===e);s&&(U(s),G(!0))})(e.id),size:"sm",variant:"outline",className:"w-full border-purple-200 text-purple-700 hover:bg-purple-50",children:[(0,a.jsx)(C,{className:"w-4 h-4 mr-1"}),"Report"]})})]})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 space-y-1 border-t border-gray-100 pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{children:["Pricing: ",e.pricingPercentage,"% commission"]}),(0,a.jsxs)("span",{children:["Created: ",O(e.createdAt)]})]})})]})},e.id)})}),(0,a.jsx)(b.lG,{open:I,onOpenChange:G,children:(0,a.jsxs)(b.Cf,{className:"md:max-w-4xl w-screen max-w-[calc(100%-2rem)] max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(b.c7,{children:[(0,a.jsxs)(b.L3,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(A.A,{className:"w-6 h-6 text-blue-600"}),"Commission Report - ",null==L?void 0:L.name]}),(0,a.jsx)(b.rr,{children:"Detailed commission and performance analytics for your AI expert"})]}),L&&(0,a.jsxs)("div",{className:"space-y-6 mt-6",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[L.imageUrl?(0,a.jsx)(n.default,{src:(0,u.L)(L.imageUrl),alt:L.name,width:64,height:64,className:"w-16 h-16 object-cover rounded-full border-3 border-white shadow-lg"}):(0,a.jsx)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg",style:{backgroundColor:"#1E3A8A"},children:H(L.labels)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:L.name}),(0,a.jsx)("p",{className:"text-gray-600",children:L.description}),(0,a.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,a.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:L.model}),(0,a.jsxs)("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:[L.pricingPercentage,"% Commission"]})]})]})]})}),F[L.id]&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)(g.Zp,{className:"p-6 border-green-200 bg-green-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-semibold text-green-800",children:"Total Commission"}),(0,a.jsx)(w.A,{className:"w-6 h-6 text-green-600"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-700",children:z(F[L.id].totalCommission)}),(0,a.jsx)("p",{className:"text-sm text-green-600 mt-1",children:"All time earnings"})]}),(0,a.jsxs)(g.Zp,{className:"p-6 border-orange-200 bg-orange-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-semibold text-orange-800",children:"Last 30 Days"}),(0,a.jsx)(k.A,{className:"w-6 h-6 text-orange-600"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-orange-700",children:z(F[L.id].last30DaysCommission)}),(0,a.jsx)("p",{className:"text-sm text-orange-600 mt-1",children:"Recent performance"})]}),(0,a.jsxs)(g.Zp,{className:"p-6 border-blue-200 bg-blue-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-800",children:"Active Users"}),(0,a.jsx)(N.A,{className:"w-6 h-6 text-blue-600"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:F[L.id].totalUsers}),(0,a.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"Total users served"})]})]}),(0,a.jsxs)(g.Zp,{className:"p-6",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,a.jsx)(P,{className:"w-5 h-5 text-purple-600"}),"Performance Breakdown"]}),F[L.id]&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-gray-700 font-medium",children:"Total Sessions"}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:F[L.id].totalSessions})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-gray-700 font-medium",children:"Total Messages"}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:F[L.id].totalMessages})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-gray-700 font-medium",children:"Avg. Messages/Session"}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:F[L.id].totalSessions>0?Math.round(F[L.id].totalMessages/F[L.id].totalSessions):0})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-gray-700 font-medium",children:"Commission Rate"}),(0,a.jsxs)("span",{className:"text-xl font-bold text-gray-900",children:[L.pricingPercentage,"%"]})]})]})]})]}),(0,a.jsxs)(g.Zp,{className:"p-6",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"w-5 h-5 text-indigo-600"}),"Expert Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Created Date"}),(0,a.jsx)("p",{className:"text-gray-900",children:O(L.createdAt)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Model"}),(0,a.jsx)("p",{className:"text-gray-900",children:L.model})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Visibility"}),(0,a.jsx)("p",{className:"text-gray-900",children:L.isPublic?"Public":"Private"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[L.labels&&L.labels.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Labels"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:L.labels.map((e,s)=>(0,a.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md",children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),(0,a.jsx)("p",{className:"text-gray-900",children:O(L.updatedAt)})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,a.jsx)(h.$,{variant:"outline",onClick:()=>G(!1),className:"px-6",children:"Close"}),(0,a.jsxs)(h.$,{onClick:()=>{G(!1),null==s||s(L)},className:"px-6 bg-blue-600 hover:bg-blue-700 text-white",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Edit Expert"]})]})]})]})})]})};var E=t(9376);let I=(0,c.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var G=t(8564);let L=(0,c.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),U=(0,c.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),T=(0,c.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),R=(0,c.A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]),z=()=>{let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)([]),[n,d]=(0,r.useState)([]),[c,o]=(0,r.useState)(!0),[m,x]=(0,r.useState)(null);(0,r.useEffect)(()=>{u()},[]);let u=async()=>{try{o(!0),x(null);let t=await i.FH.listExperts();if(t.success){var e;let a=t.experts,r={totalExperts:a.length,publicExperts:a.filter(e=>e.isPublic).length,totalUsers:0,totalRevenue:0,totalCommission:0,last30DaysCommission:0,totalSessions:0,totalMessages:0,averageRating:0,topPerformingExpert:(null==(e=a[0])?void 0:e.name)||"N/A"},n=[],c={};if(await Promise.all(a.slice(0,10).map(async e=>{try{let s=await i.FH.getExpertStats(e.id.toString());if(s.success){let t=s.stats;r.totalUsers+=t.totalUsers,r.totalRevenue+=t.totalRevenue||0,r.totalCommission+=t.totalCommission,r.last30DaysCommission+=t.last30DaysCommission,r.totalSessions+=t.totalSessions,r.totalMessages+=t.totalMessages,n.push({id:e.id,name:e.name,totalUsers:t.totalUsers,totalCommission:t.totalCommission,last30DaysCommission:t.last30DaysCommission});let a=new Date(e.createdAt).toLocaleDateString("en-US",{month:"short"});c[a]||(c[a]={revenue:0,commission:0,count:0}),c[a].revenue+=t.totalRevenue||0,c[a].commission+=t.totalCommission,c[a].count+=1}}catch(s){console.error("Failed to load stats for expert ".concat(e.id,":"),s)}})),n.sort((e,s)=>s.totalCommission-e.totalCommission),n.length>0&&(r.topPerformingExpert=n[0].name),n.length>0){let e=n.reduce((e,s)=>{let t=r.totalUsers/n.length,a=Math.min(5,Math.max(1,s.totalUsers/t*4+1));return e+a},0);r.averageRating=Number((e/n.length).toFixed(1))}else r.averageRating=0;let o=[],m=new Date;for(let e=5;e>=0;e--){let s=new Date(m.getFullYear(),m.getMonth()-e,1).toLocaleDateString("en-US",{month:"short"}),t=c[s]||{revenue:0,commission:0,count:0},a=0;if(e<5){let s=c[new Date(m.getFullYear(),m.getMonth()-e-1,1).toLocaleDateString("en-US",{month:"short"})]||{revenue:0,commission:0,count:0};s.revenue>0&&(a=Math.round((t.revenue-s.revenue)/s.revenue*100))}o.push({month:s,revenue:t.revenue,commission:t.commission,growth:a})}s(r),l(n.slice(0,5)),d(o)}else x(t.error||"Failed to load overview data")}catch(e){x(e.message||"Failed to load overview data")}finally{o(!1)}},p=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),b=e=>new Intl.NumberFormat("en-US").format(e);return c?(0,a.jsx)(g.Zp,{className:"p-8",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded"},s))})]})}):m?(0,a.jsx)(g.Zp,{className:"p-6 border-red-200 bg-red-50",children:(0,a.jsxs)("div",{className:"text-red-700",children:[(0,a.jsx)("p",{className:"font-semibold mb-2",children:"Error loading overview"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:m}),(0,a.jsx)(h.$,{onClick:u,variant:"outline",className:"border-red-300 text-red-700 hover:bg-red-100",children:"Retry"})]})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Expert Overview"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Performance dashboard and insights"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["Updated: ",new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(g.Zp,{className:"p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-blue-600 text-sm font-medium",children:"Total Experts"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:e.totalExperts}),(0,a.jsxs)("p",{className:"text-xs text-blue-700 mt-1",children:[e.publicExperts," public"]})]}),(0,a.jsx)(E.A,{className:"w-12 h-12 text-blue-600"})]})}),(0,a.jsx)(g.Zp,{className:"p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-green-600 text-sm font-medium",children:"Total Commission"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-900",children:p(e.totalCommission).replace("IDR","").trim()}),(0,a.jsxs)("p",{className:"text-xs text-green-700 mt-1",children:["Last 30 days: ",p(e.last30DaysCommission).replace("IDR","").trim()]})]}),(0,a.jsx)(w.A,{className:"w-12 h-12 text-green-600"})]})}),(0,a.jsx)(g.Zp,{className:"p-6 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-purple-600 text-sm font-medium",children:"Total Users"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:b(e.totalUsers)}),(0,a.jsx)("p",{className:"text-xs text-purple-700 mt-1",children:"Across all experts"})]}),(0,a.jsx)(N.A,{className:"w-12 h-12 text-purple-600"})]})}),(0,a.jsx)(g.Zp,{className:"p-6 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-orange-600 text-sm font-medium",children:"Total Sessions"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-orange-900",children:b(e.totalSessions)}),(0,a.jsxs)("p",{className:"text-xs text-orange-700 mt-1",children:[b(e.totalMessages)," messages"]})]}),(0,a.jsx)(I,{className:"w-12 h-12 text-orange-600"})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(g.Zp,{className:"lg:col-span-2 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-900 flex items-center",children:[(0,a.jsx)(P,{className:"w-5 h-5 mr-2 text-blue-600"}),"Top Performing Experts"]}),(0,a.jsxs)(h.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 mr-1"}),"View All"]})]}),(0,a.jsx)("div",{className:"space-y-4",children:t.length>0?t.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-bold",children:s+1}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.totalUsers," users"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-bold text-green-600",children:p(e.totalCommission).replace("IDR","").trim()}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["30d: ",p(e.last30DaysCommission).replace("IDR","").trim()]})]})]},e.id)):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(E.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No expert performance data available yet"})]})})]}),(0,a.jsxs)(g.Zp,{className:"p-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-900 mb-6 flex items-center",children:[(0,a.jsx)(A.A,{className:"w-5 h-5 mr-2 text-purple-600"}),"Quick Stats"]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(G.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900",children:"Avg. Rating"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"User satisfaction"})]})]}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.averageRating})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"w-5 h-5 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900",children:"Growth Rate"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"This month"})]})]}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:n.length>0&&n[n.length-1].growth>0?"+".concat(n[n.length-1].growth,"%"):n.length>0?"".concat(n[n.length-1].growth,"%"):"0%"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(L,{className:"w-5 h-5 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900",children:"Top Expert"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Best performer"})]})]}),(0,a.jsx)("p",{className:"text-sm font-bold text-purple-600 text-right max-w-24 truncate",children:e.topPerformingExpert})]}),(0,a.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,a.jsxs)(h.$,{className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",onClick:()=>window.open("/analytics","_blank"),children:[(0,a.jsx)(U,{className:"w-4 h-4 mr-2"}),"View Detailed Analytics"]})})]})]})]}),(0,a.jsxs)(g.Zp,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-900 flex items-center",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 mr-2 text-green-600"}),"Revenue Trend Analysis"]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)(h.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-1"}),"Last 6 Months"]})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,a.jsx)(w.A,{className:"w-5 h-5 text-blue-600 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-blue-600 font-medium",children:"Total Revenue"})]}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:p(e.totalRevenue)}),(0,a.jsx)("p",{className:"text-xs text-blue-700 mt-1",children:"All time earnings"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,a.jsx)(P,{className:"w-5 h-5 text-green-600 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-green-600 font-medium",children:"Your Commission"})]}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-900",children:p(e.totalCommission)}),(0,a.jsxs)("p",{className:"text-xs text-green-700 mt-1",children:[(e.totalCommission/Math.max(e.totalRevenue,1)*100).toFixed(1),"% of total"]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,a.jsx)(k.A,{className:"w-5 h-5 text-orange-600 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-orange-600 font-medium",children:"Last 30 Days"})]}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-900",children:p(e.last30DaysCommission)}),(0,a.jsx)("p",{className:"text-xs text-orange-700 mt-1",children:"Recent commission"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900",children:"Monthly Revenue & Commission"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Revenue"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Commission"})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-6 gap-4 items-end h-48",children:n.map(e=>{let s=Math.max(...n.map(e=>e.revenue)),t=e.revenue/s*100,l=e.commission/s*100;return(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-end space-x-1 h-32",children:[(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"w-6 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-md transition-all duration-300 hover:from-blue-600 hover:to-blue-500",style:{height:"".concat(t,"%")}}),(0,a.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)("div",{className:"bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap",children:p(e.revenue)})})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"w-6 bg-gradient-to-t from-green-500 to-green-400 rounded-t-md transition-all duration-300 hover:from-green-600 hover:to-green-500",style:{height:"".concat(l,"%")}}),(0,a.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)("div",{className:"bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap",children:p(e.commission)})})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-700",children:e.month}),(0,a.jsx)("div",{className:"flex items-center justify-center mt-1",children:e.growth>0?(0,a.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,a.jsx)(T,{className:"w-3 h-3"}),(0,a.jsxs)("span",{className:"text-xs font-medium",children:[e.growth,"%"]})]}):e.growth<0?(0,a.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,a.jsx)(R,{className:"w-3 h-3"}),(0,a.jsxs)("span",{className:"text-xs font-medium",children:[Math.abs(e.growth),"%"]})]}):(0,a.jsx)("div",{className:"flex items-center text-gray-500",children:(0,a.jsx)("span",{className:"text-xs font-medium",children:"0%"})})})]})]},e.month)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(g.Zp,{className:"p-4 border-l-4 border-green-500 bg-green-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Best Month"}),(0,a.jsx)("p",{className:"text-lg font-bold text-green-900",children:n.length>0&&n.reduce((e,s)=>e.commission>s.commission?e:s).month})]}),(0,a.jsx)(v.A,{className:"w-8 h-8 text-green-600"})]})}),(0,a.jsx)(g.Zp,{className:"p-4 border-l-4 border-blue-500 bg-blue-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Avg. Growth"}),(0,a.jsxs)("p",{className:"text-lg font-bold text-blue-900",children:[n.length>0&&Math.round(n.reduce((e,s)=>e+s.growth,0)/n.length),"%"]})]}),(0,a.jsx)(A.A,{className:"w-8 h-8 text-blue-600"})]})}),(0,a.jsx)(g.Zp,{className:"p-4 border-l-4 border-purple-500 bg-purple-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Avg. Commission Rate"}),(0,a.jsx)("p",{className:"text-lg font-bold text-purple-900",children:e.totalRevenue>0?"".concat((e.totalCommission/e.totalRevenue*100).toFixed(1),"%"):"0%"})]}),(0,a.jsx)(P,{className:"w-8 h-8 text-purple-600"})]})})]})]})]})]}):null};var O=t(4416);let H=e=>{let{view:s="overview"}=e,[t,l]=(0,r.useState)(!1),[n,i]=(0,r.useState)(!1),[d,c]=(0,r.useState)(null),[o,m]=(0,r.useState)(0),u=()=>{c(null),i(!1)};return(0,a.jsxs)("div",{className:"max-w-7xl mx-auto p-6",children:["overview"===s?(0,a.jsx)(z,{}):(0,a.jsx)(F,{onExpertSelect:e=>{console.log("Expert selected:",e)},onExpertEdit:e=>{console.log("Expert edit:",e),c(e),i(!0)},refreshTrigger:o,onCreateExpert:()=>l(!0)}),t&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Create New Expert"}),(0,a.jsx)("button",{onClick:()=>l(!1),className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(O.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)(x,{onExpertCreated:e=>{console.log("Expert created:",e),m(e=>e+1),l(!1)},onCancel:()=>l(!1)})})]})}),n&&d&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:["Edit Expert: ",d.name]}),(0,a.jsx)("button",{onClick:u,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(O.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)(p,{expert:d,onExpertUpdated:e=>{console.log("Expert updated:",e),m(e=>e+1),i(!1),c(null)},onCancel:u})})]})})]})};function Z(){let e=(0,l.useSearchParams)().get("view")||"overview";return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"overview"===e?"Expert Overview":"Manage AI Experts"}),(0,a.jsx)("p",{className:"text-gray-600",children:"overview"===e?"View comprehensive statistics and performance metrics for your AI experts":"Create and manage your AI experts"})]}),(0,a.jsx)(H,{view:e})]})})}function q(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,a.jsx)(Z,{})})}}},e=>{e.O(0,[445,874,352,431,212,573,441,964,358],()=>e(e.s=3760)),_N_E=e.O()}]);