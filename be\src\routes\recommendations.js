const express = require('express');
const router = express.Router();
const recommendationController = require('../controllers/recommendationController');
const { authenticateToken: auth } = require('../middleware/auth');
const { body, param, query } = require('express-validator');
const { handleValidationErrors } = require('../middleware/validation');

/**
 * @swagger
 * components:
 *   schemas:
 *     Recommendation:
 *       type: object
 *       properties:
 *         expert_id:
 *           type: integer
 *           description: ID of the recommended expert
 *         name:
 *           type: string
 *           description: Name of the expert
 *         description:
 *           type: string
 *           description: Expert description
 *         category:
 *           type: string
 *           description: Expert category
 *         average_rating:
 *           type: number
 *           description: Average rating of the expert
 *         total_chats:
 *           type: integer
 *           description: Total number of chats
 *         price_per_message:
 *           type: number
 *           description: Price per message
 *         score:
 *           type: number
 *           description: Recommendation score (0-1)
 *         reason:
 *           type: string
 *           description: Reason for recommendation
 *     
 *     RecommendationResponse:
 *       type: object
 *       properties:
 *         recommendations:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Recommendation'
 *         algorithm_used:
 *           type: string
 *           description: Algorithm used for recommendations
 *         total_available:
 *           type: integer
 *           description: Total available recommendations
 *         cache_hit:
 *           type: boolean
 *           description: Whether result was from cache
 *         generated_at:
 *           type: string
 *           format: date-time
 *           description: When recommendations were generated
 *         fallback:
 *           type: boolean
 *           description: Whether fallback recommendations were used
 */

/**
 * @swagger
 * /api/recommendations/personalized:
 *   get:
 *     summary: Get personalized recommendations for authenticated user
 *     tags: [Recommendations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of recommendations to return
 *       - in: query
 *         name: algorithm
 *         schema:
 *           type: string
 *           enum: [hybrid, collaborative, content, trending]
 *           default: hybrid
 *         description: Recommendation algorithm to use
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by expert category
 *       - in: query
 *         name: exclude
 *         schema:
 *           type: string
 *         description: Comma-separated list of expert IDs to exclude
 *     responses:
 *       200:
 *         description: Personalized recommendations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/RecommendationResponse'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/personalized',
  auth,
  [
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
    query('algorithm').optional().isIn(['hybrid', 'collaborative', 'content', 'trending']).withMessage('Invalid algorithm'),
    query('category').optional().isString().trim(),
    query('exclude').optional().isString()
  ],
  handleValidationErrors,
  recommendationController.getPersonalizedRecommendations
);

/**
 * @swagger
 * /api/recommendations/similar/{expertId}:
 *   get:
 *     summary: Get experts similar to a specific expert
 *     tags: [Recommendations]
 *     parameters:
 *       - in: path
 *         name: expertId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the reference expert
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 20
 *           default: 10
 *         description: Number of similar experts to return
 *     responses:
 *       200:
 *         description: Similar experts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     similar_experts:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           expert_id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           similarity_score:
 *                             type: number
 *                           category:
 *                             type: string
 *                           average_rating:
 *                             type: number
 *                     reference_expert:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         name:
 *                           type: string
 *                         category:
 *                           type: string
 *       400:
 *         description: Invalid expert ID
 *       404:
 *         description: Expert not found
 *       500:
 *         description: Server error
 */
router.get('/similar/:expertId',
  [
    param('expertId').isInt({ min: 1 }).withMessage('Expert ID must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 20 }).withMessage('Limit must be between 1 and 20')
  ],
  handleValidationErrors,
  recommendationController.getSimilarExperts
);

/**
 * @swagger
 * /api/recommendations/refresh:
 *   post:
 *     summary: Refresh recommendations cache for authenticated user
 *     tags: [Recommendations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               algorithm:
 *                 type: string
 *                 enum: [hybrid, collaborative, content, trending]
 *                 default: hybrid
 *                 description: Algorithm to use for refreshed recommendations
 *     responses:
 *       200:
 *         description: Recommendations refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     user_id:
 *                       type: integer
 *                     algorithm:
 *                       type: string
 *                     refreshed_at:
 *                       type: string
 *                       format: date-time
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       503:
 *         description: Recommendation service unavailable
 *       500:
 *         description: Server error
 */
router.post('/refresh',
  auth,
  [
    body('algorithm').optional().isIn(['hybrid', 'collaborative', 'content', 'trending']).withMessage('Invalid algorithm')
  ],
  handleValidationErrors,
  recommendationController.refreshRecommendations
);

/**
 * @swagger
 * /api/recommendations/track:
 *   post:
 *     summary: Track user interaction with recommendations
 *     tags: [Recommendations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - expert_id
 *               - interaction_type
 *             properties:
 *               expert_id:
 *                 type: integer
 *                 description: ID of the expert interacted with
 *               interaction_type:
 *                 type: string
 *                 enum: [click, dismiss, favorite, chat_start, view]
 *                 description: Type of interaction
 *               recommendation_position:
 *                 type: integer
 *                 description: Position of the recommendation in the list
 *               algorithm_used:
 *                 type: string
 *                 description: Algorithm that generated the recommendation
 *               metadata:
 *                 type: object
 *                 description: Additional metadata about the interaction
 *     responses:
 *       200:
 *         description: Interaction tracked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/track',
  auth,
  [
    body('expert_id').isInt({ min: 1 }).withMessage('Expert ID must be a positive integer'),
    body('interaction_type').isIn(['click', 'dismiss', 'favorite', 'chat_start', 'view']).withMessage('Invalid interaction type'),
    body('recommendation_position').optional().isInt({ min: 0 }).withMessage('Recommendation position must be a non-negative integer'),
    body('algorithm_used').optional().isString().trim(),
    body('metadata').optional().isObject()
  ],
  handleValidationErrors,
  recommendationController.trackInteraction
);

/**
 * @swagger
 * /api/recommendations/health:
 *   get:
 *     summary: Get recommendation service health status
 *     tags: [Recommendations]
 *     responses:
 *       200:
 *         description: Service health status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       enum: [healthy, unhealthy]
 *                     database:
 *                       type: object
 *                       properties:
 *                         connected:
 *                           type: boolean
 *                         response_time_ms:
 *                           type: number
 *                     cache:
 *                       type: object
 *                       properties:
 *                         connected:
 *                           type: boolean
 *                         response_time_ms:
 *                           type: number
 *                     statistics:
 *                       type: object
 *                       properties:
 *                         total_users:
 *                           type: integer
 *                         total_experts:
 *                           type: integer
 *                         total_interactions:
 *                           type: integer
 *                         cache_hit_rate:
 *                           type: number
 *       503:
 *         description: Service health check failed
 *       500:
 *         description: Server error
 */
router.get('/health', recommendationController.getServiceHealth);

// Admin routes (require admin authentication)
const { authenticateToken: adminAuth } = require('../middleware/auth'); // You might want to create a separate admin auth middleware

/**
 * @swagger
 * /api/recommendations/admin/sync-status:
 *   get:
 *     summary: Get data synchronization status (Admin only)
 *     tags: [Recommendations, Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Sync status retrieved successfully
 *       401:
 *         description: Unauthorized
 *       503:
 *         description: Unable to get sync status
 *       500:
 *         description: Server error
 */
router.get('/admin/sync-status', adminAuth, recommendationController.getSyncStatus);

/**
 * @swagger
 * /api/recommendations/admin/clear-cache:
 *   delete:
 *     summary: Clear recommendation cache (Admin only)
 *     tags: [Recommendations, Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cache cleared successfully
 *       401:
 *         description: Unauthorized
 *       503:
 *         description: Unable to clear cache
 *       500:
 *         description: Server error
 */
router.delete('/admin/clear-cache', adminAuth, recommendationController.clearCache);

/**
 * @swagger
 * /api/recommendations/admin/manual-sync:
 *   post:
 *     summary: Manually sync data to recommendation service (Admin only)
 *     tags: [Recommendations, Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [all, users, experts]
 *                 default: all
 *                 description: Type of data to sync
 *     responses:
 *       200:
 *         description: Manual sync completed successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/admin/manual-sync',
  adminAuth,
  [
    body('type').optional().isIn(['all', 'users', 'experts']).withMessage('Invalid sync type')
  ],
  handleValidationErrors,
  recommendationController.manualSync
);

module.exports = router;