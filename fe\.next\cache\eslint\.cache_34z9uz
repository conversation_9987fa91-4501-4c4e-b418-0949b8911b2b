[{"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\affiliate\\page.tsx": "1", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\ai-experts\\page.tsx": "2", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\api\\chat\\route.ts": "3", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\balance\\page.tsx": "4", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\chat\\page.tsx": "5", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\client-provider.tsx": "6", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\page.tsx": "7", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\page.tsx": "8", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\[shareToken]\\analytics\\page.tsx": "9", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\expert\\[id]\\page.tsx": "10", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\experts\\page.tsx": "11", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\history\\page.tsx": "12", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx": "13", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\login\\page.tsx": "14", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\page.tsx": "15", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\profile\\page.tsx": "16", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\register\\page.tsx": "17", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\shared\\[shareToken]\\page.tsx": "18", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\verify-otp\\page.tsx": "19", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\AffiliateDashboard.tsx": "20", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ApiExample.tsx": "21", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\BalanceComponent.tsx": "22", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ChatHistory.tsx": "23", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\CreateExpert.tsx": "24", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\EditExpert.tsx": "25", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertList.tsx": "26", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertMarketplace.tsx": "27", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertOverview.tsx": "28", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertPanel.tsx": "29", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertProfile.tsx": "30", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertStatsDashboard.tsx": "31", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\Navigation.tsx": "32", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\PricingCalculator.tsx": "33", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\RatingSummary.tsx": "34", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\RecommendationSection.tsx": "35", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewForm.tsx": "36", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewList.tsx": "37", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewModal.tsx": "38", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareAnalytics.tsx": "39", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareCreator.tsx": "40", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\SharedExpertLanding.tsx": "41", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareList.tsx": "42", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\SimilarExperts.tsx": "43", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\StreamingChatInterface.tsx": "44", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\alert-dialog.tsx": "45", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\alert.tsx": "46", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\avatar.tsx": "47", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\badge.tsx": "48", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\button.tsx": "49", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\card.tsx": "50", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\checkbox.tsx": "51", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\connection-status.tsx": "52", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\dialog.tsx": "53", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\dropdown-menu.tsx": "54", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\form.tsx": "55", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\input.tsx": "56", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\label.tsx": "57", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\progress.tsx": "58", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\real-time-balance.tsx": "59", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\select.tsx": "60", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\skeleton.tsx": "61", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\star-rating.tsx": "62", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\streaming-message.tsx": "63", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\tabs-simple.tsx": "64", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\tabs.tsx": "65", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\UserStatsCard.tsx": "66", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\AuthContext.tsx": "67", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\SocketContext.tsx": "68", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\use-toast.ts": "69", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useRecommendations.ts": "70", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useSharing.ts": "71", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useStreamingChat.ts": "72", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\affiliateTracker.ts": "73", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\api.ts": "74", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\recommendationService.ts": "75", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\utils.ts": "76"}, {"size": 309, "mtime": 1755135974857, "results": "77", "hashOfConfig": "78"}, {"size": 13117, "mtime": 1754403356450, "results": "79", "hashOfConfig": "78"}, {"size": 1538, "mtime": 1754029021449, "results": "80", "hashOfConfig": "78"}, {"size": 624, "mtime": 1754029021450, "results": "81", "hashOfConfig": "78"}, {"size": 11256, "mtime": 1755327927350, "results": "82", "hashOfConfig": "78"}, {"size": 395, "mtime": 1754029021451, "results": "83", "hashOfConfig": "78"}, {"size": 12428, "mtime": 1755136376388, "results": "84", "hashOfConfig": "78"}, {"size": 17332, "mtime": 1755159623934, "results": "85", "hashOfConfig": "78"}, {"size": 985, "mtime": 1755159649602, "results": "86", "hashOfConfig": "78"}, {"size": 530, "mtime": 1754403356454, "results": "87", "hashOfConfig": "78"}, {"size": 1437, "mtime": 1754029021454, "results": "88", "hashOfConfig": "78"}, {"size": 281, "mtime": 1754029021456, "results": "89", "hashOfConfig": "78"}, {"size": 2305, "mtime": 1755131813438, "results": "90", "hashOfConfig": "78"}, {"size": 7305, "mtime": 1754403356455, "results": "91", "hashOfConfig": "78"}, {"size": 4603, "mtime": 1754029021458, "results": "92", "hashOfConfig": "78"}, {"size": 24420, "mtime": 1755136629853, "results": "93", "hashOfConfig": "78"}, {"size": 18981, "mtime": 1755136368921, "results": "94", "hashOfConfig": "78"}, {"size": 15385, "mtime": 1755159665637, "results": "95", "hashOfConfig": "78"}, {"size": 9682, "mtime": 1755136036363, "results": "96", "hashOfConfig": "78"}, {"size": 16841, "mtime": 1755136547851, "results": "97", "hashOfConfig": "78"}, {"size": 5464, "mtime": 1754029021463, "results": "98", "hashOfConfig": "78"}, {"size": 14505, "mtime": 1755136434811, "results": "99", "hashOfConfig": "78"}, {"size": 13593, "mtime": 1754029021464, "results": "100", "hashOfConfig": "78"}, {"size": 29158, "mtime": 1755327590281, "results": "101", "hashOfConfig": "78"}, {"size": 15843, "mtime": 1755327949627, "results": "102", "hashOfConfig": "78"}, {"size": 26722, "mtime": 1755327901844, "results": "103", "hashOfConfig": "78"}, {"size": 15088, "mtime": 1755327916658, "results": "104", "hashOfConfig": "78"}, {"size": 26916, "mtime": 1755136480238, "results": "105", "hashOfConfig": "78"}, {"size": 4301, "mtime": 1755327961211, "results": "106", "hashOfConfig": "78"}, {"size": 18810, "mtime": 1755327938967, "results": "107", "hashOfConfig": "78"}, {"size": 10091, "mtime": 1755137880327, "results": "108", "hashOfConfig": "78"}, {"size": 8792, "mtime": 1755136170069, "results": "109", "hashOfConfig": "78"}, {"size": 6038, "mtime": 1755136142363, "results": "110", "hashOfConfig": "78"}, {"size": 4736, "mtime": 1755137535264, "results": "111", "hashOfConfig": "78"}, {"size": 11082, "mtime": 1755263166827, "results": "112", "hashOfConfig": "78"}, {"size": 4480, "mtime": 1755137478769, "results": "113", "hashOfConfig": "78"}, {"size": 8259, "mtime": 1755158803875, "results": "114", "hashOfConfig": "78"}, {"size": 4683, "mtime": 1755159599866, "results": "115", "hashOfConfig": "78"}, {"size": 17944, "mtime": 1755159833144, "results": "116", "hashOfConfig": "78"}, {"size": 9859, "mtime": 1755327973953, "results": "117", "hashOfConfig": "78"}, {"size": 14173, "mtime": 1755161124379, "results": "118", "hashOfConfig": "78"}, {"size": 15578, "mtime": 1755161869219, "results": "119", "hashOfConfig": "78"}, {"size": 6969, "mtime": 1755263228450, "results": "120", "hashOfConfig": "78"}, {"size": 15219, "mtime": 1755326979149, "results": "121", "hashOfConfig": "78"}, {"size": 5414, "mtime": 1755153583376, "results": "122", "hashOfConfig": "78"}, {"size": 1641, "mtime": 1755263442883, "results": "123", "hashOfConfig": "78"}, {"size": 1150, "mtime": 1754029021473, "results": "124", "hashOfConfig": "78"}, {"size": 1162, "mtime": 1755263430324, "results": "125", "hashOfConfig": "78"}, {"size": 2182, "mtime": 1754029021474, "results": "126", "hashOfConfig": "78"}, {"size": 2081, "mtime": 1754029021474, "results": "127", "hashOfConfig": "78"}, {"size": 1448, "mtime": 1755153490187, "results": "128", "hashOfConfig": "78"}, {"size": 2708, "mtime": 1755136020217, "results": "129", "hashOfConfig": "78"}, {"size": 4125, "mtime": 1754029021475, "results": "130", "hashOfConfig": "78"}, {"size": 2860, "mtime": 1755153556787, "results": "131", "hashOfConfig": "78"}, {"size": 3926, "mtime": 1754029021475, "results": "132", "hashOfConfig": "78"}, {"size": 988, "mtime": 1754029021476, "results": "133", "hashOfConfig": "78"}, {"size": 635, "mtime": 1754029021476, "results": "134", "hashOfConfig": "78"}, {"size": 851, "mtime": 1754029021477, "results": "135", "hashOfConfig": "78"}, {"size": 6448, "mtime": 1755136018428, "results": "136", "hashOfConfig": "78"}, {"size": 3469, "mtime": 1755158675757, "results": "137", "hashOfConfig": "78"}, {"size": 274, "mtime": 1755263418890, "results": "138", "hashOfConfig": "78"}, {"size": 2195, "mtime": 1755137457200, "results": "139", "hashOfConfig": "78"}, {"size": 5894, "mtime": 1755136306330, "results": "140", "hashOfConfig": "78"}, {"size": 3654, "mtime": 1754029021478, "results": "141", "hashOfConfig": "78"}, {"size": 2035, "mtime": 1754029021478, "results": "142", "hashOfConfig": "78"}, {"size": 4253, "mtime": 1754029021471, "results": "143", "hashOfConfig": "78"}, {"size": 6597, "mtime": 1754029021479, "results": "144", "hashOfConfig": "78"}, {"size": 7637, "mtime": 1755327109378, "results": "145", "hashOfConfig": "78"}, {"size": 1843, "mtime": 1755153478679, "results": "146", "hashOfConfig": "78"}, {"size": 7560, "mtime": 1755271793337, "results": "147", "hashOfConfig": "78"}, {"size": 12233, "mtime": 1755163725221, "results": "148", "hashOfConfig": "78"}, {"size": 9327, "mtime": 1755327213204, "results": "149", "hashOfConfig": "78"}, {"size": 4815, "mtime": 1754029021480, "results": "150", "hashOfConfig": "78"}, {"size": 14638, "mtime": 1755327512592, "results": "151", "hashOfConfig": "78"}, {"size": 6407, "mtime": 1755265118250, "results": "152", "hashOfConfig": "78"}, {"size": 377, "mtime": 1754403356469, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3zk8ff", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\affiliate\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\ai-experts\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\api\\chat\\route.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\balance\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\chat\\page.tsx", [], ["382"], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\client-provider.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\[shareToken]\\analytics\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\expert\\[id]\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\experts\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\history\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\login\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\profile\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\register\\page.tsx", [], ["383"], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\shared\\[shareToken]\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\verify-otp\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\AffiliateDashboard.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ApiExample.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\BalanceComponent.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ChatHistory.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\CreateExpert.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\EditExpert.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertList.tsx", [], ["384"], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertMarketplace.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertOverview.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertPanel.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertProfile.tsx", [], ["385"], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertStatsDashboard.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\Navigation.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\PricingCalculator.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\RatingSummary.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\RecommendationSection.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewForm.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewList.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewModal.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareAnalytics.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareCreator.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\SharedExpertLanding.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareList.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\SimilarExperts.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\StreamingChatInterface.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\alert-dialog.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\alert.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\avatar.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\badge.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\button.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\card.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\checkbox.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\connection-status.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\dialog.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\dropdown-menu.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\form.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\input.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\label.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\progress.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\real-time-balance.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\select.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\skeleton.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\star-rating.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\streaming-message.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\tabs-simple.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\tabs.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\UserStatsCard.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\SocketContext.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\use-toast.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useRecommendations.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useSharing.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useStreamingChat.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\affiliateTracker.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\api.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\recommendationService.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\utils.ts", [], [], {"ruleId": "386", "severity": 1, "message": "387", "line": 48, "column": 6, "nodeType": "388", "endLine": 48, "endColumn": 26, "suggestions": "389", "suppressions": "390"}, {"ruleId": "386", "severity": 1, "message": "391", "line": 48, "column": 6, "nodeType": "388", "endLine": 48, "endColumn": 20, "suggestions": "392", "suppressions": "393"}, {"ruleId": "386", "severity": 1, "message": "394", "line": 142, "column": 6, "nodeType": "388", "endLine": 142, "endColumn": 22, "suggestions": "395", "suppressions": "396"}, {"ruleId": "386", "severity": 1, "message": "397", "line": 70, "column": 6, "nodeType": "388", "endLine": 70, "endColumn": 16, "suggestions": "398", "suppressions": "399"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'initializeChatWithExpert' and 'loadChatHistory'. Either include them or remove the dependency array.", "ArrayExpression", ["400"], ["401"], "React Hook useEffect has a missing dependency: 'checkAffiliateTracking'. Either include it or remove the dependency array.", ["402"], ["403"], "React Hook useEffect has a missing dependency: 'loadExperts'. Either include it or remove the dependency array.", ["404"], ["405"], "React Hook useEffect has a missing dependency: 'loadExpert'. Either include it or remove the dependency array.", ["406"], ["407"], {"desc": "408", "fix": "409"}, {"kind": "410", "justification": "411"}, {"desc": "412", "fix": "413"}, {"kind": "410", "justification": "411"}, {"desc": "414", "fix": "415"}, {"kind": "410", "justification": "411"}, {"desc": "416", "fix": "417"}, {"kind": "410", "justification": "411"}, "Update the dependencies array to be: [expertId, initializeChatWithExpert, loadChatHistory, threadId]", {"range": "418", "text": "419"}, "directive", "", "Update the dependencies array to be: [checkAffiliateTracking, searchParams]", {"range": "420", "text": "421"}, "Update the dependencies array to be: [loadExperts, refreshTrigger]", {"range": "422", "text": "423"}, "Update the dependencies array to be: [expertId, loadExpert]", {"range": "424", "text": "425"}, [1588, 1608], "[expertId, initializeChatWithExpert, loadChatHistory, threadId]", [1395, 1409], "[checkAffiliateTracking, searchParams]", [3807, 3823], "[loadExperts, refreshTrigger]", [2078, 2088], "[expertId, loadExpert]"]