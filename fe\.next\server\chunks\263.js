exports.id=263,exports.ids=[263],exports.modules={3537:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,47429,23)),Promise.resolve().then(c.bind(c,59740)),Promise.resolve().then(c.bind(c,88928)),Promise.resolve().then(c.bind(c,29131)),Promise.resolve().then(c.bind(c,43708))},29131:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\AuthContext.tsx","useAuth")},29614:(a,b,c)=>{"use strict";c.d(b,{F:()=>i,SocketProvider:()=>j});var d=c(60687),e=c(43210),f=c(57405),g=c(63213);let h=(0,e.createContext)({socket:null,isConnected:!1,connectionError:null,joinChat:()=>{},leaveChat:()=>{},sendChatMessage:()=>{},currentChatRoom:null,reconnect:()=>{}}),i=()=>{let a=(0,e.useContext)(h);if(!a)throw Error("useSocket must be used within a SocketProvider");return a},j=({children:a})=>{let{token:b,isAuthenticated:c,isLoading:i}=(0,g.A)(),[j,k]=(0,e.useState)(null),[l,m]=(0,e.useState)(!1),[n,o]=(0,e.useState)(null),[p,q]=(0,e.useState)(null),r=(0,e.useRef)(0);(0,e.useEffect)(()=>{if(console.log("\uD83D\uDD0C Socket initialization check:",{isLoading:i,isAuthenticated:c,hasToken:!!b,tokenLength:b?.length,tokenStart:b?.substring(0,20)+"..."}),i)return void console.log("\uD83D\uDD0C Auth still loading, waiting...");if(j&&(console.log("\uD83E\uDDF9 Cleaning up existing socket connection"),j.disconnect(),k(null),m(!1),q(null)),!c||!b){console.log("\uD83D\uDD0C User not authenticated, skipping socket connection"),o("Please log in to use real-time chat");return}let a=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001";console.log("\uD83D\uDD0C Initializing Socket.IO connection to:",a);let d=(0,f.io)(a,{auth:{token:b},transports:["websocket","polling"],timeout:2e4,reconnection:!0,reconnectionAttempts:5,reconnectionDelay:1e3,reconnectionDelayMax:5e3});return d.on("connect",()=>{console.log("✅ Socket connected:",d.id),m(!0),o(null),r.current=0}),d.on("disconnect",a=>{console.log("❌ Socket disconnected:",a),m(!1),q(null),"io server disconnect"===a&&d.connect()}),d.on("connect_error",a=>{console.error("\uD83D\uDD0C Socket connection error:",a),console.error("\uD83D\uDD0C Error details:",{message:a.message,name:a.name,stack:a.stack}),o(a.message||"Connection failed"),m(!1),r.current+=1,r.current>=5&&o("Failed to connect after multiple attempts")}),d.on("chat_joined",a=>{console.log("\uD83C\uDFE0 Joined chat room:",a),q(a.room)}),d.on("chat_error",a=>{console.error("\uD83D\uDCAC Chat error:",a)}),d.on("typing_start",a=>{console.log("⌨️ User started typing:",a)}),d.on("typing_stop",a=>{console.log("⌨️ User stopped typing:",a)}),d.on("error",a=>{console.error("\uD83D\uDD10 Socket authentication error:",a),a.message&&a.message.includes("token")?(o("Authentication failed - please log in again"),localStorage.removeItem("token")):o("Connection failed"),m(!1)}),k(d),()=>{console.log("\uD83E\uDDF9 Cleaning up socket connection on unmount/change"),d.disconnect()}},[i,c,b,j]);let s=(0,e.useCallback)((a,b)=>{if(!j||!l)return void console.warn("⚠️ Cannot join chat: socket not connected");console.log("\uD83C\uDFE0 Joining chat:",{expertId:a,sessionId:b}),j.emit("join_chat",{expertId:a,sessionId:b})},[j,l]),t=(0,e.useCallback)(()=>{if(!j||!l)return void console.warn("⚠️ Cannot leave chat: socket not connected");console.log("\uD83D\uDEAA Leaving chat"),j.emit("leave_chat"),q(null)},[j,l]),u=(0,e.useCallback)((a,b,c)=>{if(!j||!l)return void console.warn("⚠️ Cannot send message: socket not connected");console.log("\uD83D\uDCE4 Sending chat message:",{message:a.substring(0,50)+"...",expertId:b,sessionId:c}),j.emit("start_chat_stream",{message:a,expertId:b,sessionId:c})},[j,l]),v=(0,e.useCallback)(()=>{console.log("\uD83D\uDD04 Manual reconnection requested"),j&&j.disconnect(),k(null),m(!1),o(null),q(null),r.current=0,b&&setTimeout(()=>{console.log("\uD83D\uDD04 Reconnection will be handled by useEffect")},100)},[j,b]);return(0,d.jsx)(h.Provider,{value:{socket:j,isConnected:l,connectionError:n,joinChat:s,leaveChat:t,sendChatMessage:u,currentChatRoom:p,reconnect:v},children:a})}},39727:()=>{},43708:(a,b,c)=>{"use strict";c.d(b,{SocketProvider:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\SocketContext.tsx","useSocket");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call SocketProvider() from the server but SocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\SocketContext.tsx","SocketProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\contexts\\\\SocketContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\SocketContext.tsx","default")},47990:()=>{},58532:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},59740:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\app\\\\client-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\client-provider.tsx","default")},61135:()=>{},62185:(a,b,c)=>{"use strict";c.d(b,{FH:()=>h,H2:()=>g,R2:()=>i,SP:()=>f});var d=c(51060);let e=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",f=d.A.create({baseURL:e,headers:{"Content-Type":"application/json"},timeout:3e4});async function g(a,b={}){let{method:c="GET",body:d,headers:e={},skipAuth:h=!1}=b,i={method:c.toLowerCase(),url:a,headers:{...e},metadata:{skipAuth:h}};d&&"GET"!==c&&(i.data=d);try{return(await f(i)).data}catch(a){throw a}}f.interceptors.request.use(a=>(console.log("\uD83D\uDD0D API Call Debug:",{endpoint:a.url,fullUrl:`${e}${a.url}`,API_URL:e,hasToken:!1,tokenPreview:"none",method:a.method?.toUpperCase(),data:a.data,"process.env.NEXT_PUBLIC_API_URL":process.env.NEXT_PUBLIC_API_URL}),a),a=>(console.error("\uD83D\uDCA5 Request interceptor error:",a),Promise.reject(a))),f.interceptors.response.use(a=>(console.log("\uD83D\uDCE1 Response status:",a.status,a.statusText),console.log("✅ API Success:",a.data),a),a=>{throw console.error("❌ API Error:",a.response?.data||a.message),console.error("\uD83D\uDCA5 API call failed:",a),Error(a.response?.data?.message||a.message||`HTTP error! status: ${a.response?.status}`)});let h={get:(a,b={})=>g(a,{...b,method:"GET"}),post:(a,b={})=>g(a,{...b,method:"POST"}),put:(a,b={})=>g(a,{...b,method:"PUT"}),delete:(a,b={})=>g(a,{...b,method:"DELETE"}),health:()=>g("/health"),chat:(a,b,c,d)=>g("/api/chat",{method:"POST",body:{message:a,threadId:b,expertId:c,expertContext:d}}),getThreadMessages:a=>g(`/api/thread/${a}/messages`),getSessionMessages:(a,b)=>g(`/api/chat/sessions/${a}/messages${b?`?limit=${b}`:""}`),getUserChatSessions:a=>g(`/api/chat/sessions${a?`?limit=${a}`:""}`),getUserStats:()=>g("/api/chat/stats"),getActiveSessionForExpert:a=>g(`/api/chat/sessions/expert/${a}`),updateSessionTitle:(a,b)=>g(`/api/chat/sessions/${a}/title`,{method:"PUT",body:{title:b}}),deleteSession:a=>g(`/api/chat/sessions/${a}`,{method:"DELETE"}),createThread:()=>g("/assistant/thread",{method:"POST"}),sendMessage:(a,b)=>g("/assistant/message",{method:"POST",body:{threadId:a,message:b}}),runAssistant:a=>g("/assistant/run",{method:"POST",body:{threadId:a}}),getMessages:a=>g(`/assistant/messages/${a}`),createExpert:async a=>{try{return(await d.A.post(`${e}/api/experts`,a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(a){throw Error(a.response?.data?.message||a.message||`HTTP error! status: ${a.response?.status}`)}},listExperts:()=>g("/api/experts"),getPublicExperts:()=>g("/api/experts/public",{skipAuth:!0}),getExpert:a=>g(`/api/experts/${a}`),updateExpert:async(a,b,c,f)=>{let g=new FormData;Object.keys(b).forEach(a=>{void 0!==b[a]&&null!==b[a]&&("labels"===a&&Array.isArray(b[a])?g.append(a,JSON.stringify(b[a])):g.append(a,b[a].toString()))}),c&&g.append("file",c),f&&g.append("image",f);try{return(await d.A.put(`${e}/api/experts/${a}`,g,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(a){throw Error(a.response?.data?.message||a.message||`HTTP error! status: ${a.response?.status}`)}},getAvailableModels:()=>g("/api/models"),getModelPricing:a=>g(`/api/models/${a}/pricing`),calculateCost:(a,b,c,d)=>g("/api/calculate-cost",{method:"POST",body:{model:a,inputTokens:b,outputTokens:c,pricingPercentage:d}}),getExpertStats:a=>g(`/api/experts/${a}/stats`),createReview:a=>g("/api/reviews",{method:"POST",body:a}),updateReview:(a,b)=>g(`/api/reviews/${a}`,{method:"PUT",body:b}),getExpertReviews:(a,b=1,c=10)=>g(`/api/reviews/expert/${a}?page=${b}&limit=${c}`,{skipAuth:!0}),getUserReviews:(a=1,b=10)=>g(`/api/reviews/my?page=${a}&limit=${b}`),getReview:a=>g(`/api/reviews/${a}`,{skipAuth:!0}),canUserReview:a=>g(`/api/reviews/expert/${a}/can-review`),getExpertRatingStats:a=>g(`/api/reviews/expert/${a}/stats`,{skipAuth:!0})},i={register:a=>g("/api/users/register",{method:"POST",body:a,skipAuth:!0}),verifyOTP:a=>g("/api/users/verify-otp",{method:"POST",body:a,skipAuth:!0}),login:a=>g("/api/users/login",{method:"POST",body:a,skipAuth:!0}),getProfile:()=>g("/api/users/profile"),updateProfile:a=>g("/api/users/profile",{method:"PUT",body:a}),changePassword:a=>g("/api/users/change-password",{method:"POST",body:a}),resendOTP:a=>g("/api/users/resend-otp",{method:"POST",body:{phone:a},skipAuth:!0}),forgotPassword:a=>g("/api/users/forgot-password",{method:"POST",body:{phone:a},skipAuth:!0}),resetPassword:(a,b,c)=>g("/api/users/reset-password",{method:"POST",body:{phone:a,code:b,newPassword:c},skipAuth:!0}),logout:()=>g("/api/users/logout",{method:"POST"}),getBalanceSummary:()=>g("/api/balance/summary"),getPointTransactions:a=>g(`/api/balance/transactions/points${a?`?limit=${a}`:""}`),getCreditTransactions:a=>g(`/api/balance/transactions/credits${a?`?limit=${a}`:""}`),checkAffordability:a=>g("/api/balance/can-afford",{method:"POST",body:{amount:a}}),addPoints:(a,b)=>g("/api/balance/points/add",{method:"POST",body:{amount:a,description:b}}),addCredits:(a,b)=>g("/api/balance/credits/add",{method:"POST",body:{amount:a,description:b}})}},62742:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(92314),f=c(8693),g=c(43210);function h({children:a}){let[b]=(0,g.useState)(()=>new e.E);return(0,d.jsx)(f.Ht,{client:b,children:a})}},63213:(a,b,c)=>{"use strict";c.d(b,{A:()=>i,AuthProvider:()=>h});var d=c(60687),e=c(43210),f=c(62185);let g=(0,e.createContext)(void 0),h=({children:a})=>{let[b,c]=(0,e.useState)(null),[h,i]=(0,e.useState)(null),[j,k]=(0,e.useState)(!0);(0,e.useEffect)(()=>{(async()=>{let a=localStorage.getItem("token"),b=localStorage.getItem("user");if(a&&b)try{i(a),c(JSON.parse(b));let d=await f.R2.getProfile();c(d.user)}catch(a){console.error("Token validation failed:",a),localStorage.removeItem("token"),localStorage.removeItem("user"),i(null),c(null)}k(!1)})()},[]);let l=async(a,b)=>{try{let d=(await f.R2.login({phone:a,password:b})).user,e=d.token,g={user_id:d.user_id,phone:d.phone,name:d.name,email:d.email};c(g),i(e),localStorage.setItem("token",e),localStorage.setItem("user",JSON.stringify(g))}catch(a){throw Error(a.message||"Login failed")}},m=async a=>{try{return await f.R2.register(a)}catch(a){throw Error(a.message||"Registration failed")}},n=async(a,b)=>{try{let d=(await f.R2.verifyOTP({phone:a,code:b})).user,e=d.token,g={user_id:d.user_id,phone:d.phone,name:d.name,email:d.email};c(g),i(e),localStorage.setItem("token",e),localStorage.setItem("user",JSON.stringify(g))}catch(a){throw Error(a.message||"OTP verification failed")}},o=async()=>{try{h&&await f.R2.logout()}catch(a){console.error("Logout API call failed:",a)}finally{c(null),i(null),localStorage.removeItem("token"),localStorage.removeItem("user")}},p=async a=>{try{return await f.R2.resendOTP(a)}catch(a){throw Error(a.message||"Failed to resend OTP")}},q=async a=>{try{return await f.R2.forgotPassword(a)}catch(a){throw Error(a.message||"Failed to request password reset")}},r=async(a,b,c)=>{try{await f.R2.resetPassword(a,b,c)}catch(a){throw Error(a.message||"Password reset failed")}};return(0,d.jsx)(g.Provider,{value:{user:b,token:h,isLoading:j,isAuthenticated:!!b&&!!h,login:l,register:m,verifyOTP:n,logout:o,resendOTP:p,forgotPassword:q,resetPassword:r},children:a})},i=()=>{let a=(0,e.useContext)(g);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a}},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},82500:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},86246:(a,b,c)=>{"use strict";c.d(b,{default:()=>q});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(43210),i=c(58869),j=c(41312),k=c(35583),l=c(78272),m=c(40083),n=c(98712),o=c(23026),p=c(63213);let q=()=>{let a=(0,g.usePathname)(),b=(0,g.useRouter)(),{user:c,isAuthenticated:e,logout:q}=(0,p.A)(),[r,s]=(0,h.useState)(!1),[t,u]=(0,h.useState)(!1),v=[{href:"/ai-experts",label:"AI Experts"},...e?[{href:"/history",label:"History"}]:[]],w=[{href:"/profile",label:"My Profile",icon:i.A},{href:"/affiliate",label:"Affiliate Program",icon:j.A},{href:"/balance",label:"Saldo",icon:k.A}],x=async()=>{await q(),u(!1),b.push("/")};return(0,d.jsx)("nav",{className:"bg-white shadow-lg border-b border-gray-100",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,d.jsx)(f(),{href:"/",className:"text-2xl font-bold",style:{color:"#1E3A8A"},children:"PakarAI"}),(0,d.jsxs)("div",{className:"flex space-x-6",children:[v.map(b=>(0,d.jsx)(f(),{href:b.href,className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${a===b.href?"text-white shadow-lg":"text-gray-600 hover:text-white hover:shadow-md"}`,style:a===b.href?{backgroundColor:"#1E3A8A"}:{backgroundColor:"transparent"},onMouseEnter:c=>{a!==b.href&&(c.currentTarget.style.backgroundColor="#1E3A8A")},onMouseLeave:c=>{a!==b.href&&(c.currentTarget.style.backgroundColor="transparent")},children:b.label},b.href)),e&&(0,d.jsxs)("div",{className:"relative",onMouseEnter:()=>s(!0),onMouseLeave:()=>s(!1),children:[(0,d.jsxs)("button",{className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-1 ${a.startsWith("/experts")?"text-white shadow-lg":"text-gray-600 hover:text-white hover:shadow-md"}`,style:a.startsWith("/experts")?{backgroundColor:"#1E3A8A"}:{backgroundColor:"transparent"},onMouseEnter:b=>{a.startsWith("/experts")||(b.currentTarget.style.backgroundColor="#1E3A8A")},onMouseLeave:b=>{a.startsWith("/experts")||(b.currentTarget.style.backgroundColor="transparent")},children:["My Experts",(0,d.jsx)(l.A,{className:"w-4 h-4"})]}),r&&(0,d.jsx)("div",{className:"absolute top-full left-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[{href:"/experts?view=overview",label:"Overview"},{href:"/experts?view=manage",label:"Manage Expert"}].map(a=>(0,d.jsx)(f(),{href:a.href,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",children:a.label},a.href))})]})]})]}),e&&c?(0,d.jsxs)("div",{className:"relative",onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),children:[(0,d.jsxs)("button",{className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:(0,d.jsx)(i.A,{className:"w-4 h-4 text-white"})}),(0,d.jsx)("span",{className:"hidden md:block",children:c.name}),(0,d.jsx)(l.A,{className:"w-4 h-4"})]}),t&&(0,d.jsxs)("div",{className:"absolute top-full right-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,d.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:c.name}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:c.email})]}),(0,d.jsxs)(f(),{href:"/dashboard",className:"flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",children:[(0,d.jsx)(i.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Dashboard"})]}),w.map(a=>{let b=a.icon;return(0,d.jsxs)(f(),{href:a.href,className:"flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",children:[(0,d.jsx)(b,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:a.label})]},a.href)}),(0,d.jsx)("hr",{className:"my-1 border-gray-200"}),(0,d.jsxs)("button",{onClick:x,className:"flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors w-full text-left",children:[(0,d.jsx)(m.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Logout"})]})]})]}):(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)(f(),{href:"/login",className:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors",children:[(0,d.jsx)(n.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Login"})]}),(0,d.jsxs)(f(),{href:"/register",className:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",children:[(0,d.jsx)(o.A,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Sign Up"})]})]})]})})})}},88928:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\Web\\\\pakarai\\\\ai-trainer\\\\fe\\\\src\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\Navigation.tsx","default")},93809:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,79167,23)),Promise.resolve().then(c.bind(c,62742)),Promise.resolve().then(c.bind(c,86246)),Promise.resolve().then(c.bind(c,63213)),Promise.resolve().then(c.bind(c,29614))},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o,metadata:()=>n});var d=c(37413),e=c(22376),f=c.n(e),g=c(68726),h=c.n(g);c(61135);var i=c(59740),j=c(88928),k=c(29131),l=c(43708),m=c(36162);let n={title:"AI Expert Marketplace - AI Trainer Hub",description:"Connect with specialized AI experts tailored to your needs. From business consulting to creative solutions."};function o({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsxs)("body",{className:`${f().variable} ${h().variable} antialiased`,children:[(0,d.jsx)(k.AuthProvider,{children:(0,d.jsx)(i.default,{children:(0,d.jsxs)(l.SocketProvider,{children:[(0,d.jsx)(j.default,{}),a]})})}),(0,d.jsx)(m.default,{id:"affiliate-tracker",strategy:"afterInteractive",children:`
            // Initialize affiliate tracking
            if (typeof window !== 'undefined') {
              const initTracking = async () => {
                const urlParams = new URLSearchParams(window.location.search);
                const refCode = urlParams.get('ref');
                
                if (refCode) {
                  try {
                    const response = await fetch('/api/affiliate/track/' + refCode, {
                      method: 'GET',
                      credentials: 'include'
                    });
                    
                    if (response.ok) {
                      console.log('Affiliate tracking successful');
                    }
                  } catch (error) {
                    console.error('Affiliate tracking failed:', error);
                  }
                }
              };
              
              initTracking();
            }
          `})]})})}}};