(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{103:(e,t,s)=>{Promise.resolve().then(s.bind(s,9584))},1366:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},6932:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7580:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8564:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9584:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(5155),r=s(2115),l=s(7924),i=s(9946);let n=(0,i.A)("arrow-up-narrow-wide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]),c=(0,i.A)("arrow-down-wide-narrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]]);var o=s(6932),d=s(8564),x=s(7580),m=s(1366),h=s(6874),g=s.n(h);let u=()=>{let[e,t]=(0,r.useState)([]),[s,i]=(0,r.useState)([]),[h,u]=(0,r.useState)(""),[p,b]=(0,r.useState)("rating"),[f,y]=(0,r.useState)("desc"),[j,w]=(0,r.useState)([]),[N,v]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=[{id:1,name:"Dr. Sarah Johnson",title:"AI Research Specialist",description:"Expert in machine learning and neural networks with 10+ years experience.",rating:4.9,totalChats:1250,followers:3400,tags:["Machine Learning","Neural Networks","Research"]},{id:2,name:"Mark Chen",title:"Data Science Consultant",description:"Specialized in data analysis, visualization, and predictive modeling.",rating:4.7,totalChats:890,followers:2100,tags:["Data Science","Visualization","Analytics"]},{id:3,name:"Emily Rodriguez",title:"NLP Expert",description:"Natural Language Processing specialist with focus on conversational AI.",rating:4.8,totalChats:1100,followers:2800,tags:["NLP","Conversational AI","Text Processing"]},{id:4,name:"David Kim",title:"Computer Vision Engineer",description:"Expert in image recognition, object detection, and computer vision applications.",rating:4.6,totalChats:675,followers:1900,tags:["Computer Vision","Image Recognition","Deep Learning"]}];t(e),i(e)},[]);let k=Array.from(new Set(e.flatMap(e=>e.tags)));(0,r.useEffect)(()=>{let t=e.filter(e=>{let t=e.name.toLowerCase().includes(h.toLowerCase())||e.title.toLowerCase().includes(h.toLowerCase())||e.description.toLowerCase().includes(h.toLowerCase()),s=0===j.length||j.some(t=>e.tags.includes(t));return t&&s});t.sort((e,t)=>{let s=0;switch(p){case"name":s=e.name.localeCompare(t.name);break;case"rating":s=e.rating-t.rating;break;case"followers":s=e.followers-t.followers}return"asc"===f?s:-s}),i(t)},[e,h,j,p,f]);let A=()=>{w([]),u("")};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"AI Experts"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Discover and connect with AI specialists"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 items-center justify-between",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,a.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",placeholder:"Search experts...",value:h,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,a.jsxs)("select",{value:p,onChange:e=>b(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"rating",children:"Rating"}),(0,a.jsx)("option",{value:"followers",children:"Followers"}),(0,a.jsx)("option",{value:"name",children:"Name"})]})]}),(0,a.jsx)("button",{onClick:()=>y("asc"===f?"desc":"asc"),className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"Sort ".concat("asc"===f?"descending":"ascending"),children:"asc"===f?(0,a.jsx)(n,{className:"w-4 h-4"}):(0,a.jsx)(c,{className:"w-4 h-4"})}),(0,a.jsxs)("button",{onClick:()=>v(!N),className:"flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ".concat(N?"bg-blue-100 text-blue-700 border border-blue-300":"border border-gray-300 hover:bg-gray-50"),children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),"Filters"]})]})]}),N&&(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:"Specialties"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:k.map(e=>(0,a.jsx)("button",{onClick:()=>(e=>{w(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])})(e),className:"px-3 py-1 rounded-full text-sm transition-colors ".concat(j.includes(e)?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200"),children:e},e))}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("button",{onClick:A,className:"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 underline",children:"Clear all filters"})})]})})})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["Showing ",s.length," of ",e.length," experts"]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center gap-1 mb-2",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.rating}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.totalChats," chats)"]})]})]})}),(0,a.jsx)("p",{className:"text-sm text-gray-700 mb-4 line-clamp-3",children:e.description}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1 mb-4",children:[e.tags.slice(0,3).map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full",children:e},e)),e.tags.length>3&&(0,a.jsxs)("span",{className:"px-2 py-1 bg-gray-50 text-gray-500 text-xs rounded-full",children:["+",e.tags.length-3," more"]})]}),(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.followers})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.totalChats})]})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(g(),{href:"/expert/".concat(e.id),className:"flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium",children:"View Profile"}),(0,a.jsx)(g(),{href:"/chat?expert=".concat(e.id),className:"flex-1 border border-blue-600 text-blue-600 text-center py-2 px-4 rounded-lg hover:bg-blue-50 transition-colors text-sm font-medium",children:"Start Chat"})]})]})},e.id))}),0===s.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(l.A,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No experts found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search criteria or filters"}),(0,a.jsx)("button",{onClick:A,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Clear all filters"})]})]})})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(2115);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:x,...m}=e;return(0,a.createElement)("svg",{ref:t,...i,width:r,height:r,stroke:s,strokeWidth:c?24*Number(n)/Number(r):n,className:l("lucide",o),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(m)&&{"aria-hidden":"true"},...m},[...x.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(d)?d:[d]])}),c=(e,t)=>{let s=(0,a.forwardRef)((s,i)=>{let{className:c,...o}=s;return(0,a.createElement)(n,{ref:i,iconNode:t,className:l("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...o})});return s.displayName=r(e),s}}},e=>{e.O(0,[874,441,964,358],()=>e(e.s=103)),_N_E=e.O()}]);