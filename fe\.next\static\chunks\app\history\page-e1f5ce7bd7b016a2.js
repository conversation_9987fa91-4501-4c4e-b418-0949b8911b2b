(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[429],{448:(e,t,s)=>{Promise.resolve().then(s.bind(s,2674))},1007:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1366:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},2525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2674:(e,t,s)=>{"use strict";s.d(t,{default:()=>u});var a=s(5155),r=s(2115),i=s(6874),n=s.n(i),o=s(5731),l=s(9946);let c=(0,l.A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),d=(0,l.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var h=s(2525),x=s(1366),m=s(1007),p=s(4186);let u=()=>{let[e,t]=(0,r.useState)([]),[s,i]=(0,r.useState)(!0),[l,u]=(0,r.useState)(null),[g,y]=(0,r.useState)(null),[v,b]=(0,r.useState)(""),j=async()=>{try{i(!0),u(null);let e=await o.FH.getUserChatSessions(50);e.success?t(e.sessions):u(e.error||"Failed to load chat sessions")}catch(e){u(e.message||"Failed to load chat sessions")}finally{i(!1)}};(0,r.useEffect)(()=>{j()},[]);let f=e=>{let t=new Date(e),s=new Date,a=Math.floor((s.getTime()-t.getTime())/36e5),r=Math.floor(a/24);return a<1?"Just now":a<24?"".concat(a,"h ago"):r<7?"".concat(r,"d ago"):t.toLocaleDateString("en-US",{month:"short",day:"numeric",year:t.getFullYear()!==s.getFullYear()?"numeric":void 0})},w=async(s,a)=>{try{(await o.FH.updateSessionTitle(s.toString(),a)).success&&(t(e.map(e=>e.id===s?{...e,session_title:a}:e)),y(null),b(""))}catch(e){console.error("Failed to update title:",e)}},N=async s=>{if(confirm("Are you sure you want to delete this chat session?"))try{(await o.FH.deleteSession(s.toString())).success&&t(e.filter(e=>e.id!==s))}catch(e){console.error("Failed to delete session:",e)}},k=()=>{y(null),b("")};return s?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(n(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,a.jsx)("span",{children:"←"}),(0,a.jsx)("span",{children:"Back to Home"})]})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Chat History"}),(0,a.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 animate-pulse",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"h-5 bg-gray-200 rounded w-1/3"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t))})]})}):l?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(n(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,a.jsx)("span",{children:"←"}),(0,a.jsx)("span",{children:"Back to Home"})]})}),(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-8 max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"text-red-600 text-6xl mb-4",children:"⚠️"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-red-800 mb-2",children:"Unable to Load Chat History"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:l}),(0,a.jsx)("button",{onClick:j,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Try Again"})]})})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(n(),{href:"/",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors",children:[(0,a.jsx)("span",{children:"←"}),(0,a.jsx)("span",{children:"Back to Home"})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Chat History"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.length," conversation",1!==e.length?"s":""]})]}),0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCAC"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Chat History Yet"}),(0,a.jsx)("p",{className:"text-gray-500 mb-6",children:"Start a conversation with an AI expert to see it here!"}),(0,a.jsx)(n(),{href:"/",className:"inline-block px-6 py-3 rounded-xl text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5",style:{backgroundColor:"#1E3A8A"},children:"Browse Experts"})]}):(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-200 group",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[g===e.id?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"text",value:v,onChange:e=>b(e.target.value),className:"flex-1 px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyDown:t=>{"Enter"===t.key?w(e.id,v):"Escape"===t.key&&k()},autoFocus:!0}),(0,a.jsx)("button",{onClick:()=>w(e.id,v),className:"px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",children:"Save"}),(0,a.jsx)("button",{onClick:k,className:"px-3 py-1 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors text-sm",children:"Cancel"})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 group-hover:text-blue-900 transition-colors",children:e.session_title}),(0,a.jsx)("button",{onClick:()=>(e=>{y(e.id),b(e.session_title)})(e),className:"opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-blue-600 transition-all",children:(0,a.jsx)(c,{className:"w-4 h-4"})})]}),e.expert_name&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsx)("div",{className:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs",style:{backgroundColor:"#1E3A8A"},children:(0,a.jsx)(d,{className:"w-3 h-3"})}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Expert: ",(0,a.jsx)("span",{className:"font-medium",children:e.expert_name})]}),(0,a.jsx)("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full",children:e.expert_model})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)(n(),{href:"/chat?threadId=".concat(e.thread_id),className:"px-4 py-2 rounded-lg text-white font-medium transition-all duration-200 hover:shadow-md",style:{backgroundColor:"#1E3A8A"},children:"Continue"}),(0,a.jsx)("button",{onClick:()=>N(e.id),className:"p-2 text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(h.A,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[e.message_count," messages"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.user_messages})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(d,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.assistant_messages})]}),e.total_cost>0&&(0,a.jsx)("div",{children:(0,a.jsxs)("span",{children:["� ",(e=>{let t=Math.round(e).toLocaleString("en-US");return"Rp ".concat(t)})(e.total_cost)]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.last_message_at?f(e.last_message_at):f(e.created_at)})]})]})]},e.id))})]})})}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5731:(e,t,s)=>{"use strict";s.d(t,{FH:()=>c,H2:()=>l,R2:()=>d,SP:()=>n});var a=s(3464),r=s(9509);let i=r.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",n=a.A.create({baseURL:i,headers:{"Content-Type":"application/json"},timeout:3e4});function o(){return localStorage.getItem("token")}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:s="GET",body:a,headers:r={},skipAuth:i=!1}=t,o={method:s.toLowerCase(),url:e,headers:{...r},metadata:{skipAuth:i}};a&&"GET"!==s&&(o.data=a);try{return(await n(o)).data}catch(e){throw e}}n.interceptors.request.use(e=>{var t,s;let a=o();return console.log("\uD83D\uDD0D API Call Debug:",{endpoint:e.url,fullUrl:"".concat(i).concat(e.url),API_URL:i,hasToken:!!a,tokenPreview:a?a.substring(0,3)+"***":"none",method:null==(t=e.method)?void 0:t.toUpperCase(),data:e.data,"process.env.NEXT_PUBLIC_API_URL":r.env.NEXT_PUBLIC_API_URL}),!a||(null==(s=e.metadata)?void 0:s.skipAuth)||(e.headers.Authorization="Bearer ".concat(a)),e},e=>(console.error("\uD83D\uDCA5 Request interceptor error:",e),Promise.reject(e))),n.interceptors.response.use(e=>(console.log("\uD83D\uDCE1 Response status:",e.status,e.statusText),console.log("✅ API Success:",e.data),e),e=>{var t,s,a,r;throw console.error("❌ API Error:",(null==(t=e.response)?void 0:t.data)||e.message),console.error("\uD83D\uDCA5 API call failed:",e),Error((null==(a=e.response)||null==(s=a.data)?void 0:s.message)||e.message||"HTTP error! status: ".concat(null==(r=e.response)?void 0:r.status))});let c={get:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l(e,{...t,method:"GET"})},post:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l(e,{...t,method:"POST"})},put:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l(e,{...t,method:"PUT"})},delete:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l(e,{...t,method:"DELETE"})},health:()=>l("/health"),chat:(e,t,s,a)=>l("/api/chat",{method:"POST",body:{message:e,threadId:t,expertId:s,expertContext:a}}),getThreadMessages:e=>l("/api/thread/".concat(e,"/messages")),getSessionMessages:(e,t)=>l("/api/chat/sessions/".concat(e,"/messages").concat(t?"?limit=".concat(t):"")),getUserChatSessions:e=>l("/api/chat/sessions".concat(e?"?limit=".concat(e):"")),getUserStats:()=>l("/api/chat/stats"),getActiveSessionForExpert:e=>l("/api/chat/sessions/expert/".concat(e)),updateSessionTitle:(e,t)=>l("/api/chat/sessions/".concat(e,"/title"),{method:"PUT",body:{title:t}}),deleteSession:e=>l("/api/chat/sessions/".concat(e),{method:"DELETE"}),createThread:()=>l("/assistant/thread",{method:"POST"}),sendMessage:(e,t)=>l("/assistant/message",{method:"POST",body:{threadId:e,message:t}}),runAssistant:e=>l("/assistant/run",{method:"POST",body:{threadId:e}}),getMessages:e=>l("/assistant/messages/".concat(e)),createExpert:async e=>{let t=o();try{return(await a.A.post("".concat(i,"/api/experts"),e,{headers:{"Content-Type":"multipart/form-data",...t?{Authorization:"Bearer ".concat(t)}:{}}})).data}catch(e){var s,r,n;throw Error((null==(r=e.response)||null==(s=r.data)?void 0:s.message)||e.message||"HTTP error! status: ".concat(null==(n=e.response)?void 0:n.status))}},listExperts:()=>l("/api/experts"),getPublicExperts:()=>l("/api/experts/public",{skipAuth:!0}),getExpert:e=>l("/api/experts/".concat(e)),updateExpert:async(e,t,s,r)=>{let n=o(),l=new FormData;Object.keys(t).forEach(e=>{void 0!==t[e]&&null!==t[e]&&("labels"===e&&Array.isArray(t[e])?l.append(e,JSON.stringify(t[e])):l.append(e,t[e].toString()))}),s&&l.append("file",s),r&&l.append("image",r);try{return(await a.A.put("".concat(i,"/api/experts/").concat(e),l,{headers:{"Content-Type":"multipart/form-data",...n?{Authorization:"Bearer ".concat(n)}:{}}})).data}catch(e){var c,d,h;throw Error((null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"HTTP error! status: ".concat(null==(h=e.response)?void 0:h.status))}},getAvailableModels:()=>l("/api/models"),getModelPricing:e=>l("/api/models/".concat(e,"/pricing")),calculateCost:(e,t,s,a)=>l("/api/calculate-cost",{method:"POST",body:{model:e,inputTokens:t,outputTokens:s,pricingPercentage:a}}),getExpertStats:e=>l("/api/experts/".concat(e,"/stats")),createReview:e=>l("/api/reviews",{method:"POST",body:e}),updateReview:(e,t)=>l("/api/reviews/".concat(e),{method:"PUT",body:t}),getExpertReviews:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return l("/api/reviews/expert/".concat(e,"?page=").concat(t,"&limit=").concat(s),{skipAuth:!0})},getUserReviews:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return l("/api/reviews/my?page=".concat(e,"&limit=").concat(t))},getReview:e=>l("/api/reviews/".concat(e),{skipAuth:!0}),canUserReview:e=>l("/api/reviews/expert/".concat(e,"/can-review")),getExpertRatingStats:e=>l("/api/reviews/expert/".concat(e,"/stats"),{skipAuth:!0})},d={register:e=>l("/api/users/register",{method:"POST",body:e,skipAuth:!0}),verifyOTP:e=>l("/api/users/verify-otp",{method:"POST",body:e,skipAuth:!0}),login:e=>l("/api/users/login",{method:"POST",body:e,skipAuth:!0}),getProfile:()=>l("/api/users/profile"),updateProfile:e=>l("/api/users/profile",{method:"PUT",body:e}),changePassword:e=>l("/api/users/change-password",{method:"POST",body:e}),resendOTP:e=>l("/api/users/resend-otp",{method:"POST",body:{phone:e},skipAuth:!0}),forgotPassword:e=>l("/api/users/forgot-password",{method:"POST",body:{phone:e},skipAuth:!0}),resetPassword:(e,t,s)=>l("/api/users/reset-password",{method:"POST",body:{phone:e,code:t,newPassword:s},skipAuth:!0}),logout:()=>l("/api/users/logout",{method:"POST"}),getBalanceSummary:()=>l("/api/balance/summary"),getPointTransactions:e=>l("/api/balance/transactions/points".concat(e?"?limit=".concat(e):"")),getCreditTransactions:e=>l("/api/balance/transactions/credits".concat(e?"?limit=".concat(e):"")),checkAffordability:e=>l("/api/balance/can-afford",{method:"POST",body:{amount:e}}),addPoints:(e,t)=>l("/api/balance/points/add",{method:"POST",body:{amount:e,description:t}}),addCredits:(e,t)=>l("/api/balance/credits/add",{method:"POST",body:{amount:e,description:t}})}}},e=>{e.O(0,[445,874,441,964,358],()=>e(e.s=448)),_N_E=e.O()}]);